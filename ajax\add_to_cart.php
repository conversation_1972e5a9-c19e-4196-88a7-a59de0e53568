<?php
require_once '../includes/config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Méthode non autorisée'
    ]);
    exit;
}

// Get product ID and quantity
$productId = isset($_POST['product_id']) ? (int)$_POST['product_id'] : 0;
$quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 1;

// Validate product ID and quantity
if ($productId <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'ID de produit invalide'
    ]);
    exit;
}

if ($quantity <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Quantité invalide'
    ]);
    exit;
}

// Initialize cart
$cart = new Cart();

// Add item to cart
$result = $cart->addItem($productId, $quantity);

if ($result) {
    echo json_encode([
        'success' => true,
        'message' => 'Produit ajouté au panier',
        'cart_count' => $cart->getItemCount()
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Impossible d\'ajouter le produit au panier'
    ]);
}
