<?php
/**
 * Database Connection Class
 * 
 * Handles database connection using PDO
 */
class Database {
    private $host = 'localhost';
    private $db_name = 'NuitBlanche';
    private $username = 'root';
    private $password = '';
    private $conn;
    private static $instance = null;
    
    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct() {
        try {
            $this->conn = new PDO(
                "mysql:host={$this->host};dbname={$this->db_name}",
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            $this->conn->exec("SET NAMES utf8");
        } catch(PDOException $e) {
            echo "Connection Error: " . $e->getMessage();
        }
    }
    
    /**
     * Get singleton instance
     * 
     * @return Database
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Get database connection
     * 
     * @return PDO
     */
    public function getConnection() {
        return $this->conn;
    }
    
    /**
     * Execute a query and return the statement
     * 
     * @param string $query
     * @param array $params
     * @return PDOStatement
     */
    public function query($query, $params = []) {
        try {
            $stmt = $this->conn->prepare($query);
            // Bind parameters with explicit types
            foreach ($params as $key => $value) {
                if ($key === 'limit' || $key === 'offset') {
                    $stmt->bindValue(":$key", (int)$value, PDO::PARAM_INT);
                } else {
                    $stmt->bindValue(":$key", $value); // Default binding for other parameters
                }
            }
            $stmt->execute();
            return $stmt;
        } catch(PDOException $e) {
            error_log("Query Error: " . $e->getMessage() . " | Query: $query | Params: " . json_encode($params));
            echo "Query Error: " . $e->getMessage();
            return false;
        }
    }
    
    /**
     * Fetch a single row
     * 
     * @param string $query
     * @param array $params
     * @return array|false
     */
    public function fetchOne($query, $params = []) {
        $stmt = $this->query($query, $params);
        return $stmt ? $stmt->fetch() : false;
    }
    
    /**
     * Fetch all rows
     * 
     * @param string $query
     * @param array $params
     * @return array|false
     */
    public function fetchAll($query, $params = []) {
        $stmt = $this->query($query, $params);
        return $stmt ? $stmt->fetchAll() : false;
    }
    
    /**
     * Insert a record and return the last insert ID
     * 
     * @param string $table
     * @param array $data
     * @return int|false
     */
    public function insert($table, $data) {
        $columns = implode(', ', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $query = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        
        if ($this->query($query, $data)) {
            return $this->conn->lastInsertId();
        }
        
        return false;
    }
    
    /**
     * Update a record
     * 
     * @param string $table
     * @param array $data
     * @param string $where
     * @param array $whereParams
     * @return bool
     */
    public function update($table, $data, $where, $whereParams = []) {
        $set = [];
        foreach (array_keys($data) as $column) {
            $set[] = "{$column} = :{$column}";
        }
        $set = implode(', ', $set);
        
        $query = "UPDATE {$table} SET {$set} WHERE {$where}";
        
        $params = array_merge($data, $whereParams);
        
        return $this->query($query, $params) ? true : false;
    }
    
    /**
     * Delete a record
     * 
     * @param string $table
     * @param string $where
     * @param array $params
     * @return bool
     */
    public function delete($table, $where, $params = []) {
        $query = "DELETE FROM {$table} WHERE {$where}";
        return $this->query($query, $params) ? true : false;
    }
}
