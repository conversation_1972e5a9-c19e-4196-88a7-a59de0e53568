<?php
require_once 'includes/config.php';

// Redirect if already logged in
$auth->redirectIfLoggedIn();

$error = '';
$success = '';
$formData = [
    'first_name' => '',
    'last_name' => '',
    'email' => '',
];

// Process registration form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $formData = [
        'first_name' => trim($_POST['first_name'] ?? ''),
        'last_name' => trim($_POST['last_name'] ?? ''),
        'email' => trim($_POST['email'] ?? ''),
    ];
    
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    // Validate form data
    if (empty($formData['first_name']) || empty($formData['last_name']) || empty($formData['email']) || empty($password)) {
        $error = 'Veuillez remplir tous les champs obligatoires.';
    } elseif (!filter_var($formData['email'], FILTER_VALIDATE_EMAIL)) {
        $error = 'Veuillez entrer une adresse email valide.';
    } elseif (strlen($password) < 8) {
        $error = 'Le mot de passe doit contenir au moins 8 caractères.';
    } elseif ($password !== $confirmPassword) {
        $error = 'Les mots de passe ne correspondent pas.';
    } else {
        $user = new User();
        
        // Add password to user data
        $userData = $formData;
        $userData['password'] = $password;
        
        // Register user
        $userId = $user->register($userData);
        
        if ($userId) {
            $success = 'Votre compte a été créé avec succès. Vous pouvez maintenant vous connecter.';
            
            // Clear form data
            $formData = [
                'first_name' => '',
                'last_name' => '',
                'email' => '',
            ];
        } else {
            $error = 'Cette adresse email est déjà utilisée.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Créer un compte - <?= SITE_NAME ?></title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="register-page">
    <?php include 'includes/header.php'; ?>
    
    <main>
        <section class="auth-section">
            <h1>Créer un compte</h1>
            
            <?php if ($error): ?>
                <div class="error-message"><?= $error ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="success-message">
                    <?= $success ?>
                    <p><a href="login.php">Se connecter</a></p>
                </div>
            <?php else: ?>
                <form class="auth-form" method="POST" action="">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="first_name">Prénom *</label>
                            <input type="text" id="first_name" name="first_name" value="<?= htmlspecialchars($formData['first_name']) ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="last_name">Nom *</label>
                            <input type="text" id="last_name" name="last_name" value="<?= htmlspecialchars($formData['last_name']) ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" id="email" name="email" value="<?= htmlspecialchars($formData['email']) ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Mot de passe *</label>
                        <input type="password" id="password" name="password" required>
                        <small>Le mot de passe doit contenir au moins 8 caractères.</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password">Confirmer le mot de passe *</label>
                        <input type="password" id="confirm_password" name="confirm_password" required>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn">Créer un compte</button>
                    </div>
                    
                    <div class="auth-links">
                        <p>Vous avez déjà un compte ? <a href="login.php">Se connecter</a></p>
                    </div>
                </form>
            <?php endif; ?>
        </section>
    </main>
    
    <?php include 'includes/footer.php'; ?>
    
    <script src="js/main.js"></script>
</body>
</html>
