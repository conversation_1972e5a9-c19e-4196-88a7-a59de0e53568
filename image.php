<?php
/**
 * Image Server for Blob Data
 * 
 * Serves images stored as blob data in the database
 */

require_once 'includes/config.php';

// Get product ID from URL parameter
$productId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Validate product ID
if ($productId <= 0) {
    http_response_code(404);
    exit('Image not found');
}

try {
    // Get database instance
    $db = Database::getInstance();
    
    // Fetch the image blob data
    $result = $db->fetchOne(
        "SELECT ImageFile, name FROM products WHERE id = :id AND ImageFile IS NOT NULL AND ImageFile != ''",
        ['id' => $productId]
    );
    
    if (!$result || empty($result['ImageFile'])) {
        http_response_code(404);
        exit('Image not found');
    }
    
    $imageData = $result['ImageFile'];
    $productName = $result['name'];
    
    // Detect image type from blob data
    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $mimeType = $finfo->buffer($imageData);
    
    // Validate mime type
    if (!in_array($mimeType, ALLOWED_IMAGE_TYPES)) {
        http_response_code(415);
        exit('Unsupported image type');
    }
    
    // Set appropriate headers
    header('Content-Type: ' . $mimeType);
    header('Content-Length: ' . strlen($imageData));
    header('Cache-Control: public, max-age=31536000'); // Cache for 1 year
    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    
    // Set filename for download (optional)
    $filename = preg_replace('/[^a-zA-Z0-9-_\.]/', '', str_replace(' ', '-', $productName));
    $extension = '';
    switch ($mimeType) {
        case 'image/jpeg':
        case 'image/jpg':
            $extension = '.jpg';
            break;
        case 'image/png':
            $extension = '.png';
            break;
        case 'image/gif':
            $extension = '.gif';
            break;
        case 'image/webp':
            $extension = '.webp';
            break;
    }
    header('Content-Disposition: inline; filename="' . $filename . $extension . '"');
    
    // Output the image data
    echo $imageData;
    
} catch (Exception $e) {
    error_log('Image server error: ' . $e->getMessage());
    http_response_code(500);
    exit('Internal server error');
}
?>
