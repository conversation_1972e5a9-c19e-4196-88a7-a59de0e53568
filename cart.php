<?php
require_once 'includes/config.php';

// Initialize cart
$cart = new Cart();
$cartItems = $cart->getItems();
$cartTotal = $cart->getTotal();

// Fixed shipping cost
$shippingCost = 15.00;

// Get recommended products
$productObj = new Product();
$recommendedProducts = $productObj->getFeatured(3);
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panier - <?= SITE_NAME ?></title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="cart-page">
    <?php include 'includes/header.php'; ?>

    <main>
        <section class="cart-section">
            <h1>Votre Panier</h1>

            <div class="cart-container">
                <?php if (empty($cartItems)): ?>
                    <div class="empty-cart">
                        <h2>Votre panier est vide</h2>
                        <p>Découvrez notre collection et ajoutez des articles à votre panier.</p>
                        <a href="shop.php" class="btn">Continuer vos achats</a>
                    </div>
                <?php else: ?>
                    <div class="cart-items">
                        <?php foreach ($cartItems as $item): ?>
                            <div class="cart-item" data-product-id="<?= $item['product_id'] ?>">
                                <div class="item-image">
                                    <a href="product.php?slug=<?= $item['slug'] ?>">
                                        <img src="<?= $item['image'] ?>" alt="<?= $item['name'] ?>">
                                    </a>
                                </div>
                                <div class="item-details">
                                    <h3><a href="product.php?slug=<?= $item['slug'] ?>"><?= $item['name'] ?></a></h3>
                                    <p class="item-price">
                                        <?php if ($item['sale_price']): ?>
                                            <span class="original-price">$<?= number_format($item['price'], 2) ?></span>
                                            <span class="sale-price">$<?= number_format($item['sale_price'], 2) ?></span>
                                        <?php else: ?>
                                            $<?= number_format($item['price'], 2) ?>
                                        <?php endif; ?>
                                    </p>
                                    <div class="item-quantity">
                                        <button class="quantity-btn minus" data-product-id="<?= $item['product_id'] ?>">-</button>
                                        <input type="number" value="<?= $item['quantity'] ?>" min="1" max="10" data-product-id="<?= $item['product_id'] ?>">
                                        <button class="quantity-btn plus" data-product-id="<?= $item['product_id'] ?>">+</button>
                                    </div>
                                </div>
                                <div class="item-total">
                                    <p>
                                        $<?= number_format(($item['sale_price'] ? $item['sale_price'] : $item['price']) * $item['quantity'], 2) ?>
                                    </p>
                                </div>
                                <button class="remove-item" data-product-id="<?= $item['product_id'] ?>">×</button>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="cart-summary">
                        <h2>Résumé</h2>

                        <div class="summary-row">
                            <span>Sous-total</span>
                            <span id="subtotal">$<?= number_format($cartTotal, 2) ?></span>
                        </div>

                        <div class="summary-row">
                            <span>Livraison</span>
                            <span id="shipping">$<?= number_format($shippingCost, 2) ?></span>
                        </div>

                        <div class="summary-row total">
                            <span>Total</span>
                            <span id="total">$<?= number_format($cartTotal + $shippingCost, 2) ?></span>
                        </div>

                        <div class="promo-code">
                            <input type="text" placeholder="Code promo" id="promo-code">
                            <button class="btn-secondary" id="apply-promo">Appliquer</button>
                        </div>

                        <button class="btn checkout-btn" id="checkout-btn">Procéder au paiement</button>

                        <div class="continue-shopping">
                            <a href="shop.php">Continuer vos achats</a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </section>

        <?php if (!empty($recommendedProducts)): ?>
            <section class="recommended-products">
                <h2>Vous pourriez aussi aimer</h2>

                <div class="products-grid">
                    <?php foreach ($recommendedProducts as $product): ?>
                        <div class="product-card <?= $product['sale_price'] ? 'sale' : '' ?>">
                            <?php if ($product['sale_price']): ?>
                                <div class="discount-badge">
                                    -<?= round((($product['price'] - $product['sale_price']) / $product['price']) * 100) ?>%
                                </div>
                            <?php endif; ?>

                            <a href="product.php?slug=<?= $product['slug'] ?>">
                                <img src="<?= $product['image'] ?>" alt="<?= $product['name'] ?>">
                                <h3><?= $product['name'] ?></h3>
                                <p><?= substr($product['description'], 0, 80) . (strlen($product['description']) > 80 ? '...' : '') ?></p>
                                <div class="price">
                                    <?php if ($product['sale_price']): ?>
                                        <span class="original-price">$<?= number_format($product['price'], 2) ?></span>
                                        <span class="sale-price">$<?= number_format($product['sale_price'], 2) ?></span>
                                    <?php else: ?>
                                        <span>$<?= number_format($product['price'], 2) ?></span>
                                    <?php endif; ?>
                                </div>
                            </a>

                            <button class="btn-secondary add-to-cart" data-product-id="<?= $product['id'] ?>">
                                Ajouter au panier
                            </button>
                        </div>
                    <?php endforeach; ?>
                </div>
            </section>
        <?php endif; ?>
    </main>

    <?php include 'includes/footer.php'; ?>

    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Quantity buttons functionality
            const minusButtons = document.querySelectorAll('.quantity-btn.minus');
            const plusButtons = document.querySelectorAll('.quantity-btn.plus');
            const quantityInputs = document.querySelectorAll('.item-quantity input');
            const removeButtons = document.querySelectorAll('.remove-item');
            const checkoutButton = document.getElementById('checkout-btn');
            const applyPromoButton = document.getElementById('apply-promo');
            const recommendedAddToCartButtons = document.querySelectorAll('.recommended-products .add-to-cart');

            // Minus button click
            minusButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.getAttribute('data-product-id');
                    const input = this.nextElementSibling;
                    let value = parseInt(input.value);

                    if (value > 1) {
                        value--;
                        input.value = value;
                        updateCartItem(productId, value);
                    }
                });
            });

            // Plus button click
            plusButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.getAttribute('data-product-id');
                    const input = this.previousElementSibling;
                    let value = parseInt(input.value);

                    if (value < 10) {
                        value++;
                        input.value = value;
                        updateCartItem(productId, value);
                    }
                });
            });

            // Quantity input change
            quantityInputs.forEach(input => {
                input.addEventListener('change', function() {
                    const productId = this.getAttribute('data-product-id');
                    let value = parseInt(this.value);

                    if (isNaN(value) || value < 1) {
                        value = 1;
                        this.value = value;
                    } else if (value > 10) {
                        value = 10;
                        this.value = value;
                    }

                    updateCartItem(productId, value);
                });
            });

            // Remove item button click
            removeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.getAttribute('data-product-id');
                    removeCartItem(productId);
                });
            });

            // Checkout button click
            if (checkoutButton) {
                checkoutButton.addEventListener('click', function() {
                    // Redirect to checkout page
                    window.location.href = 'checkout.php';
                });
            }

            // Apply promo code button click
            if (applyPromoButton) {
                applyPromoButton.addEventListener('click', function() {
                    const promoCode = document.getElementById('promo-code').value.trim();

                    if (promoCode) {
                        // Simulate promo code application
                        alert('Code promo appliqué avec succès!');

                        // Add discount row to summary
                        const summaryRows = document.querySelector('.cart-summary');
                        const totalRow = document.querySelector('.summary-row.total');

                        // Check if discount row already exists
                        let discountRow = document.querySelector('.summary-row.discount');

                        if (!discountRow) {
                            discountRow = document.createElement('div');
                            discountRow.className = 'summary-row discount';
                            discountRow.innerHTML = '<span>Réduction</span><span id="discount">-$30.00</span>';

                            // Insert before total row
                            summaryRows.insertBefore(discountRow, totalRow);
                        }

                        // Update total
                        updateCartSummary();
                    } else {
                        alert('Veuillez entrer un code promo');
                    }
                });
            }

            // Recommended products add to cart buttons
            recommendedAddToCartButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.getAttribute('data-product-id');

                    // AJAX request to add item to cart
                    fetch('ajax/add_to_cart.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'product_id=' + productId + '&quantity=1'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success message
                            alert('Produit ajouté au panier');

                            // Reload page to update cart
                            window.location.reload();
                        } else {
                            alert(data.message || 'Une erreur est survenue');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Une erreur est survenue');
                    });
                });
            });

            // Helper functions
            function updateCartItem(productId, quantity) {
                // AJAX request to update cart item
                fetch('ajax/update_cart.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'product_id=' + productId + '&quantity=' + quantity
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update item total
                        updateItemTotal(productId);

                        // Update cart summary
                        updateCartSummary();

                        // Update cart count in header
                        updateCartCount(data.cart_count);
                    } else {
                        alert(data.message || 'Une erreur est survenue');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Une erreur est survenue');
                });
            }

            function removeCartItem(productId) {
                // AJAX request to remove cart item
                fetch('ajax/update_cart.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'product_id=' + productId + '&action=remove'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove item from DOM
                        const cartItem = document.querySelector(`.cart-item[data-product-id="${productId}"]`);
                        cartItem.remove();

                        // Update cart summary
                        updateCartSummary();

                        // Update cart count in header
                        updateCartCount(data.cart_count);

                        // Check if cart is empty
                        const cartItems = document.querySelectorAll('.cart-item');
                        if (cartItems.length === 0) {
                            showEmptyCart();
                        }
                    } else {
                        alert(data.message || 'Une erreur est survenue');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Une erreur est survenue');
                });
            }

            function updateItemTotal(productId) {
                const cartItem = document.querySelector(`.cart-item[data-product-id="${productId}"]`);
                const priceElement = cartItem.querySelector('.item-price');
                const quantityInput = cartItem.querySelector('.item-quantity input');
                const totalElement = cartItem.querySelector('.item-total p');

                let price;
                if (priceElement.querySelector('.sale-price')) {
                    price = parseFloat(priceElement.querySelector('.sale-price').textContent.replace('$', ''));
                } else {
                    price = parseFloat(priceElement.textContent.replace('$', ''));
                }

                const quantity = parseInt(quantityInput.value);
                const total = price * quantity;

                totalElement.textContent = '$' + total.toFixed(2);
            }

            function updateCartSummary() {
                // Calculate subtotal
                let subtotal = 0;
                document.querySelectorAll('.cart-item').forEach(item => {
                    const totalElement = item.querySelector('.item-total p');
                    subtotal += parseFloat(totalElement.textContent.replace('$', ''));
                });

                // Update subtotal
                document.getElementById('subtotal').textContent = '$' + subtotal.toFixed(2);

                // Get shipping cost
                const shipping = parseFloat(document.getElementById('shipping').textContent.replace('$', ''));

                // Check for discount
                let discount = 0;
                const discountElement = document.querySelector('#discount');
                if (discountElement) {
                    discount = parseFloat(discountElement.textContent.replace('$', '').replace('-', ''));
                }

                // Update total
                const total = subtotal + shipping - discount;
                document.getElementById('total').textContent = '$' + total.toFixed(2);
            }

            function updateCartCount(count) {
                const cartCount = document.querySelector('.cart-count');
                if (cartCount) {
                    if (count > 0) {
                        cartCount.textContent = count;
                    } else {
                        cartCount.remove();
                    }
                }
            }

            function showEmptyCart() {
                const cartContainer = document.querySelector('.cart-container');
                cartContainer.innerHTML = `
                    <div class="empty-cart">
                        <h2>Votre panier est vide</h2>
                        <p>Découvrez notre collection et ajoutez des articles à votre panier.</p>
                        <a href="shop.php" class="btn">Continuer vos achats</a>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
