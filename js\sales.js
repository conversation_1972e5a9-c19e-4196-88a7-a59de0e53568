/**
 * Nuit Blanche - Sales Page JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    const categorySelect = document.querySelector('.filter-group:first-child select');
    const sortSelect = document.querySelector('.filter-group:last-child select');
    const productsGrid = document.querySelector('.products-grid');
    
    if (categorySelect && sortSelect && productsGrid) {
        // Category filter
        categorySelect.addEventListener('change', function() {
            const category = this.value;
            console.log(`Filtering by category: ${category}`);
            
            // Simulate filtering (in a real app, this would filter the products)
            if (category === 'all') {
                document.querySelectorAll('.product-card').forEach(card => {
                    card.style.display = 'block';
                });
            } else {
                // This is just a simulation - in a real app, products would have category data attributes
                alert(`Filtrage par catégorie: ${category} (simulation)`);
            }
        });
        
        // Sort filter
        sortSelect.addEventListener('change', function() {
            const sortBy = this.value;
            console.log(`Sorting by: ${sortBy}`);
            
            // Get all product cards
            const productCards = Array.from(document.querySelectorAll('.product-card'));
            
            // Sort based on selected option
            switch (sortBy) {
                case 'price-low':
                    // Sort by price (low to high)
                    productCards.sort((a, b) => {
                        const priceA = parseFloat(a.querySelector('.sale-price').textContent.replace('$', ''));
                        const priceB = parseFloat(b.querySelector('.sale-price').textContent.replace('$', ''));
                        return priceA - priceB;
                    });
                    break;
                    
                case 'price-high':
                    // Sort by price (high to low)
                    productCards.sort((a, b) => {
                        const priceA = parseFloat(a.querySelector('.sale-price').textContent.replace('$', ''));
                        const priceB = parseFloat(b.querySelector('.sale-price').textContent.replace('$', ''));
                        return priceB - priceA;
                    });
                    break;
                    
                case 'discount':
                    // Sort by discount percentage
                    productCards.sort((a, b) => {
                        const discountA = parseFloat(a.querySelector('.discount-badge').textContent.replace('%', '').replace('-', ''));
                        const discountB = parseFloat(b.querySelector('.discount-badge').textContent.replace('%', '').replace('-', ''));
                        return discountB - discountA;
                    });
                    break;
                    
                default:
                    // Featured (default order)
                    // In a real app, this would restore the original order
                    break;
            }
            
            // Re-append cards in the new order
            productCards.forEach(card => {
                productsGrid.appendChild(card);
            });
        });
    }
    
    // Add to cart buttons
    const addToCartButtons = document.querySelectorAll('.btn-secondary');
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productCard = this.closest('.product-card');
            const productName = productCard.querySelector('h3').textContent;
            const productPrice = productCard.querySelector('.sale-price').textContent;
            
            console.log(`Adding to cart: ${productName} - ${productPrice}`);
            
            // Show confirmation
            const confirmation = document.createElement('div');
            confirmation.className = 'cart-confirmation';
            confirmation.innerHTML = `<p>${productName} a été ajouté au panier</p>`;
            
            document.body.appendChild(confirmation);
            
            // Remove confirmation after 3 seconds
            setTimeout(() => {
                confirmation.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(confirmation);
                }, 500);
            }, 3000);
        });
    });
    
    // Newsletter form
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const emailInput = this.querySelector('input[type="email"]');
            const email = emailInput.value.trim();
            
            if (email && isValidEmail(email)) {
                // Simulate form submission
                const submitButton = this.querySelector('button[type="submit"]');
                const originalText = submitButton.textContent;
                
                submitButton.textContent = 'Inscription en cours...';
                submitButton.disabled = true;
                
                setTimeout(() => {
                    emailInput.value = '';
                    submitButton.textContent = 'Inscrit!';
                    
                    setTimeout(() => {
                        submitButton.textContent = originalText;
                        submitButton.disabled = false;
                    }, 2000);
                }, 1500);
            } else {
                alert('Veuillez entrer une adresse email valide');
            }
        });
    }
    
    // Helper function to validate email
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
});
