/**
 * Nuit Blanche - Cart Page JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Quantity buttons functionality
    const minusButtons = document.querySelectorAll('.quantity-btn.minus');
    const plusButtons = document.querySelectorAll('.quantity-btn.plus');
    
    minusButtons.forEach(button => {
        button.addEventListener('click', function() {
            const input = this.nextElementSibling;
            let value = parseInt(input.value);
            if (value > 1) {
                input.value = value - 1;
                updateItemTotal(this.closest('.cart-item'));
                updateCartSummary();
            }
        });
    });
    
    plusButtons.forEach(button => {
        button.addEventListener('click', function() {
            const input = this.previousElementSibling;
            let value = parseInt(input.value);
            if (value < 10) {
                input.value = value + 1;
                updateItemTotal(this.closest('.cart-item'));
                updateCartSummary();
            }
        });
    });
    
    // Quantity input change
    const quantityInputs = document.querySelectorAll('.item-quantity input');
    quantityInputs.forEach(input => {
        input.addEventListener('change', function() {
            let value = parseInt(this.value);
            if (isNaN(value) || value < 1) {
                this.value = 1;
            } else if (value > 10) {
                this.value = 10;
            }
            updateItemTotal(this.closest('.cart-item'));
            updateCartSummary();
        });
    });
    
    // Remove item buttons
    const removeButtons = document.querySelectorAll('.remove-item');
    removeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const cartItem = this.closest('.cart-item');
            cartItem.classList.add('removing');
            
            setTimeout(() => {
                cartItem.remove();
                updateCartSummary();
                
                // Check if cart is empty
                const cartItems = document.querySelectorAll('.cart-item');
                if (cartItems.length === 0) {
                    showEmptyCart();
                }
            }, 300);
        });
    });
    
    // Promo code button
    const promoButton = document.querySelector('.promo-code button');
    if (promoButton) {
        promoButton.addEventListener('click', function() {
            const promoInput = document.querySelector('.promo-code input');
            const promoCode = promoInput.value.trim();
            
            if (promoCode) {
                // Simulate promo code application
                alert('Code promo appliqué avec succès!');
                promoInput.value = '';
                
                // Update summary with discount
                const summaryRows = document.querySelector('.cart-summary');
                
                // Check if discount row already exists
                let discountRow = document.querySelector('.summary-row.discount');
                if (!discountRow) {
                    discountRow = document.createElement('div');
                    discountRow.className = 'summary-row discount';
                    discountRow.innerHTML = '<span>Réduction</span><span>-$30.00</span>';
                    
                    // Insert before total
                    const totalRow = document.querySelector('.summary-row.total');
                    summaryRows.insertBefore(discountRow, totalRow);
                }
                
                updateCartSummary();
            } else {
                alert('Veuillez entrer un code promo valide');
            }
        });
    }
    
    // Checkout button
    const checkoutButton = document.querySelector('.checkout-btn');
    if (checkoutButton) {
        checkoutButton.addEventListener('click', function() {
            alert('Redirection vers la page de paiement...');
            // Redirect to checkout page
            // window.location.href = 'checkout.php';
        });
    }
    
    // Helper functions
    function updateItemTotal(cartItem) {
        const priceElement = cartItem.querySelector('.item-price');
        const quantityInput = cartItem.querySelector('.item-quantity input');
        const totalElement = cartItem.querySelector('.item-total p');
        
        const price = parseFloat(priceElement.textContent.replace('$', ''));
        const quantity = parseInt(quantityInput.value);
        const total = price * quantity;
        
        totalElement.textContent = '$' + total.toFixed(2);
    }
    
    function updateCartSummary() {
        const itemTotals = document.querySelectorAll('.item-total p');
        let subtotal = 0;
        
        itemTotals.forEach(item => {
            subtotal += parseFloat(item.textContent.replace('$', ''));
        });
        
        // Update subtotal
        const subtotalElement = document.querySelector('.summary-row:first-child span:last-child');
        subtotalElement.textContent = '$' + subtotal.toFixed(2);
        
        // Check for discount
        let discount = 0;
        const discountElement = document.querySelector('.summary-row.discount span:last-child');
        if (discountElement) {
            discount = parseFloat(discountElement.textContent.replace('$', '').replace('-', ''));
        }
        
        // Get shipping cost
        const shippingElement = document.querySelector('.summary-row:nth-child(2) span:last-child');
        const shipping = parseFloat(shippingElement.textContent.replace('$', ''));
        
        // Update total
        const totalElement = document.querySelector('.summary-row.total span:last-child');
        const total = subtotal + shipping - discount;
        totalElement.textContent = '$' + total.toFixed(2);
    }
    
    function showEmptyCart() {
        const cartContainer = document.querySelector('.cart-container');
        cartContainer.innerHTML = `
            <div class="empty-cart">
                <h2>Votre panier est vide</h2>
                <p>Découvrez notre collection et ajoutez des articles à votre panier.</p>
                <a href="shop.php" class="btn">Continuer vos achats</a>
            </div>
        `;
    }
});
