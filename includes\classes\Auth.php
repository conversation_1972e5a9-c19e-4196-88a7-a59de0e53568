<?php
/**
 * Auth Class
 *
 * Handles authentication and session management
 */
class Auth {
    private static $instance = null;

    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct() {
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    /**
     * Get singleton instance
     *
     * @return Auth
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Set user session after successful login
     *
     * @param array $user
     * @return void
     */
    public function setUser($user) {
        $_SESSION['user'] = $user;
        $_SESSION['logged_in'] = true;
    }

    /**
     * Check if user is logged in
     *
     * @return bool
     */
    public function isLoggedIn() {
        return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
    }

    /**
     * Get current user data
     *
     * @return array|null
     */
    public function getUser() {
        return $this->isLoggedIn() ? $_SESSION['user'] : null;
    }

    /**
     * Get current user ID
     *
     * @return int|null
     */
    public function getUserId() {
        $user = $this->getUser();
        return $user ? $user['id'] : null;
    }

    /**
     * Logout user
     *
     * @return void
     */
    public function logout() {
        // Unset all session variables
        $_SESSION = [];

        // Destroy the session
        session_destroy();
    }

    /**
     * Redirect if not logged in
     *
     * @param string $redirectTo
     * @return void
     */
    public function requireLogin($redirectTo = 'login.php') {
        if (!$this->isLoggedIn()) {
            header("Location: {$redirectTo}");
            exit;
        }
    }

    /**
     * Redirect if already logged in
     *
     * @param string $redirectTo
     * @return void
     */
    public function redirectIfLoggedIn($redirectTo = 'index.php') {
        if ($this->isLoggedIn()) {
            header("Location: {$redirectTo}");
            exit;
        }
    }

    /**
     * Get current user role
     *
     * @return string|null
     */
    public function getUserRole() {
        $user = $this->getUser();
        return $user ? $user['role'] : null;
    }

    /**
     * Check if user has admin role
     *
     * @return bool
     */
    public function isAdmin() {
        return $this->getUserRole() === '"ADMIN"';
    }

    /**
     * Check if user has livreur role
     *
     * @return bool
     */
    public function isLivreur() {
        return $this->getUserRole() === '"LIVREUR"';
    }

    /**
     * Require admin access
     *
     * @param string $redirectTo
     * @return void
     */
    public function requireAdmin($redirectTo = null) {
        if (!$this->isLoggedIn() || !$this->isAdmin()) {
            if ($redirectTo === null) {
                // Determine redirect path based on current directory
                $currentDir = basename(dirname($_SERVER['SCRIPT_NAME']));
                $redirectTo = ($currentDir === 'admin' || $currentDir === 'livreur') ? '../index.php' : 'index.php';
            }
            header("Location: {$redirectTo}");
            exit;
        }
    }

    /**
     * Require livreur access
     *
     * @param string $redirectTo
     * @return void
     */
    public function requireLivreur($redirectTo = null) {
        if (!$this->isLoggedIn() || !$this->isLivreur()) {
            if ($redirectTo === null) {
                // Determine redirect path based on current directory
                $currentDir = basename(dirname($_SERVER['SCRIPT_NAME']));
                $redirectTo = ($currentDir === 'admin' || $currentDir === 'livreur') ? '../index.php' : 'index.php';
            }
            header("Location: {$redirectTo}");
            exit;
        }
    }

    /**
     * Require admin or livreur access
     *
     * @param string $redirectTo
     * @return void
     */
    public function requireStaff($redirectTo = null) {
        if (!$this->isLoggedIn() || (!$this->isAdmin() && !$this->isLivreur())) {
            if ($redirectTo === null) {
                // Determine redirect path based on current directory
                $currentDir = basename(dirname($_SERVER['SCRIPT_NAME']));
                $redirectTo = ($currentDir === 'admin' || $currentDir === 'livreur') ? '../index.php' : 'index.php';
            }
            header("Location: {$redirectTo}");
            exit;
        }
    }
}
