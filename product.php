<?php
require_once 'includes/config.php';

// Get product slug from URL
$slug = isset($_GET['slug']) ? $_GET['slug'] : '';

// Initialize Product class
$productObj = new Product();

// Get product details
$product = $productObj->getBySlug($slug);

// If product not found, redirect to shop page
if (!$product) {
    header('Location: shop.php');
    exit;
}

// Get related products
$relatedProducts = $productObj->getRelated($product['id'], 6);

// Get product images
$productImages = $productObj->getImages($product['id']);

// Set page title
$pageTitle = $product['name'];
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - <?= SITE_NAME ?></title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="product-page">
    <?php include 'includes/header.php'; ?>

    <main>
        <div class="product-container">
            <div class="product-images">
                <div class="main-image">
                    <img src="<?= $productObj->getImageUrl($product) ?>" alt="<?= $product['name'] ?>">
                </div>

                <?php if (!empty($productImages)): ?>
                    <div class="product-thumbnails">
                        <?php foreach ($productImages as $image): ?>
                            <img src="<?= $image['image_path'] ?>" alt="<?= $product['name'] ?>" class="<?= $image['is_primary'] ? 'active' : '' ?>">
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="product-details">
                <h1><?= $product['name'] ?></h1>

                <div class="product-price">
                    <?php if ($product['sale_price']): ?>
                        <span class="original-price">$<?= number_format($product['price'], 2) ?></span>
                        <span class="sale-price">$<?= number_format($product['sale_price'], 2) ?></span>
                    <?php else: ?>
                        <span>$<?= number_format($product['price'], 2) ?></span>
                    <?php endif; ?>
                </div>

                <p class="product-description"><?= $product['description'] ?></p>

                <div class="product-category">
                    <span>Catégorie: </span>
                    <a href="shop.php?category=<?= $product['category_slug'] ?? '' ?>"><?= $product['category_name'] ?? 'Non catégorisé' ?></a>
                </div>

                <div class="product-stock">
                    <?php if ($product['stock'] > 0): ?>
                        <span class="in-stock">En stock (<?= $product['stock'] ?>)</span>
                    <?php else: ?>
                        <span class="out-of-stock">Rupture de stock</span>
                    <?php endif; ?>
                </div>

                <div class="product-quantity">
                    <label for="quantity">Quantité:</label>
                    <div class="quantity-control">
                        <button type="button" class="quantity-btn minus">-</button>
                        <input type="number" id="quantity" name="quantity" value="1" min="1" max="<?= $product['stock'] ?>">
                        <button type="button" class="quantity-btn plus">+</button>
                    </div>
                </div>

                <div class="product-actions">
                    <button class="btn btn-primary" id="add-to-cart" data-product-id="<?= $product['id'] ?>" <?= $product['stock'] <= 0 ? 'disabled' : '' ?>>
                        <?= $product['stock'] > 0 ? 'Ajouter au panier' : 'Indisponible' ?>
                    </button>
                    <button class="btn-secondary add-to-wishlist" data-product-id="<?= $product['id'] ?>">
                        <i class="heart-icon"></i> Ajouter aux favoris
                    </button>
                </div>
            </div>
        </div>

        <?php if (!empty($relatedProducts)): ?>
            <section class="complementary-products">
                <h2>Complétez votre look</h2>

                <div class="products-grid">
                    <?php foreach ($relatedProducts as $relatedProduct): ?>
                        <div class="product-card <?= $relatedProduct['sale_price'] ? 'sale' : '' ?>">
                            <?php if ($relatedProduct['sale_price']): ?>
                                <div class="discount-badge">
                                    -<?= round((($relatedProduct['price'] - $relatedProduct['sale_price']) / $relatedProduct['price']) * 100) ?>%
                                </div>
                            <?php endif; ?>

                            <a href="product.php?slug=<?= $relatedProduct['slug'] ?>">
                                <img src="<?= $productObj->getImageUrl($relatedProduct) ?>" alt="<?= $relatedProduct['name'] ?>">
                                <h3><?= $relatedProduct['name'] ?></h3>
                                <p><?= substr($relatedProduct['description'], 0, 80) . (strlen($relatedProduct['description']) > 80 ? '...' : '') ?></p>
                                <div class="price">
                                    <?php if ($relatedProduct['sale_price']): ?>
                                        <span class="original-price">$<?= number_format($relatedProduct['price'], 2) ?></span>
                                        <span class="sale-price">$<?= number_format($relatedProduct['sale_price'], 2) ?></span>
                                    <?php else: ?>
                                        <span>$<?= number_format($relatedProduct['price'], 2) ?></span>
                                    <?php endif; ?>
                                </div>
                            </a>

                            <button class="btn-secondary add-to-cart" data-product-id="<?= $relatedProduct['id'] ?>">
                                Ajouter au panier
                            </button>
                        </div>
                    <?php endforeach; ?>
                </div>
            </section>
        <?php endif; ?>
    </main>

    <?php include 'includes/footer.php'; ?>

    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Quantity controls
            const minusBtn = document.querySelector('.quantity-btn.minus');
            const plusBtn = document.querySelector('.quantity-btn.plus');
            const quantityInput = document.querySelector('#quantity');
            const maxQuantity = <?= $product['stock'] ?>;

            if (minusBtn && plusBtn && quantityInput) {
                minusBtn.addEventListener('click', function() {
                    let value = parseInt(quantityInput.value);
                    if (value > 1) {
                        quantityInput.value = value - 1;
                    }
                });

                plusBtn.addEventListener('click', function() {
                    let value = parseInt(quantityInput.value);
                    if (value < maxQuantity) {
                        quantityInput.value = value + 1;
                    }
                });

                quantityInput.addEventListener('change', function() {
                    let value = parseInt(this.value);
                    if (isNaN(value) || value < 1) {
                        this.value = 1;
                    } else if (value > maxQuantity) {
                        this.value = maxQuantity;
                    }
                });
            }

            // Add to cart button
            const addToCartBtn = document.querySelector('#add-to-cart');
            if (addToCartBtn) {
                addToCartBtn.addEventListener('click', function() {
                    const productId = this.getAttribute('data-product-id');
                    const quantity = parseInt(quantityInput.value);

                    // AJAX request to add item to cart
                    fetch('ajax/add_to_cart.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'product_id=' + productId + '&quantity=' + quantity
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success message
                            alert('Produit ajouté au panier');

                            // Update cart count
                            const cartCount = document.querySelector('.cart-count');
                            if (cartCount) {
                                cartCount.textContent = data.cart_count;
                            } else {
                                const cartIcon = document.querySelector('.cart-icon');
                                const newCartCount = document.createElement('span');
                                newCartCount.className = 'cart-count';
                                newCartCount.textContent = data.cart_count;
                                cartIcon.appendChild(newCartCount);
                            }
                        } else {
                            alert(data.message || 'Une erreur est survenue');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Une erreur est survenue');
                    });
                });
            }

            // Related products add to cart buttons
            const relatedAddToCartButtons = document.querySelectorAll('.products-grid .add-to-cart');
            relatedAddToCartButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.getAttribute('data-product-id');

                    // AJAX request to add item to cart
                    fetch('ajax/add_to_cart.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'product_id=' + productId + '&quantity=1'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success message
                            alert('Produit ajouté au panier');

                            // Update cart count
                            const cartCount = document.querySelector('.cart-count');
                            if (cartCount) {
                                cartCount.textContent = data.cart_count;
                            } else {
                                const cartIcon = document.querySelector('.cart-icon');
                                const newCartCount = document.createElement('span');
                                newCartCount.className = 'cart-count';
                                newCartCount.textContent = data.cart_count;
                                cartIcon.appendChild(newCartCount);
                            }
                        } else {
                            alert(data.message || 'Une erreur est survenue');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Une erreur est survenue');
                    });
                });
            });
        });
    </script>
</body>
</html>