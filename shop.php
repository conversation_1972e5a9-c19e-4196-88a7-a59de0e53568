<?php
require_once 'includes/config.php';

// Initialize Product class
$productObj = new Product();

// Get category filter
$categorySlug = isset($_GET['category']) ? $_GET['category'] : '';
$category = $categorySlug ? $productObj->getCategoryBySlug($categorySlug) : null;

// Get search term
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Get sort option
$sortBy = isset($_GET['sort']) ? $_GET['sort'] : '';

// Set page title
$pageTitle = 'Boutique';
if ($category) {
    $pageTitle = $category['name'];
} elseif ($search) {
    $pageTitle = 'Résultats pour "' . htmlspecialchars($search) . '"';
}

// Get products
$options = [
    'category_slug' => $categorySlug,
    'search' => $search,
    'sort_by' => $sortBy
];

$products = $productObj->getAll($options);

// Get sale products
$saleProducts = $productObj->getOnSale(3);

// Get all categories for filter (including hierarchical structure)
$categories = $productObj->getAllCategories();
$categoryHierarchy = $productObj->getCategoryHierarchy();
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - <?= SITE_NAME ?></title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="shop-page">
    <?php include 'includes/header.php'; ?>

    <main>
        <div class="shop-banner">
            <img src="images/products/paysage nuit blanche.jpeg" alt="Collection Nuit Blanche">
            <div class="banner-text">La mode commence là où le jour s'éteint</div>
        </div>

        <?php if (!$categorySlug && !$search): ?>
            <section class="sales-section">
                <div class="sales-content">
                    <h2>Soldes</h2>
                    <p>Des remises qui vont jusqu'à 25%</p>
                    <a href="sales.php" class="btn-secondary">Articles soldés</a>
                </div>
                <div class="sales-image">
                    <img src="images/products/soldes.jpg" alt="Soldes">
                </div>
            </section>

            <section class="perfume-section">
                <div class="perfume-image">
                    <img src="images/products/fabric frame nuit blanche.jpg" alt="Parfum Blanche">
                </div>
                <div class="perfume-content">
                    <h2>Découvrez aussi notre ligne de parfums</h2>
                    <p>Nos parfums sont développés sur la base d'une philosophie minimaliste et élégante</p>
                    <a href="perfumes.php" class="btn-secondary">voir les parfums</a>
                </div>
            </section>
        <?php endif; ?>

        <section class="product-filters">
            <h2><?= $pageTitle ?></h2>

            <div class="filter-container">
                <div class="filter-group">
                    <label>Catégorie</label>
                    <select id="category-filter" onchange="window.location.href=this.value">
                        <option value="shop.php">Toutes les catégories</option>
                        <?php foreach ($categoryHierarchy as $mainCat): ?>
                            <option value="shop.php?category=<?= $mainCat['slug'] ?>" <?= $categorySlug === $mainCat['slug'] ? 'selected' : '' ?>>
                                <?= $mainCat['name'] ?>
                            </option>
                            <?php foreach ($mainCat['subcategories'] as $subCat): ?>
                                <option value="shop.php?category=<?= $subCat['slug'] ?>" <?= $categorySlug === $subCat['slug'] ? 'selected' : '' ?>>
                                    &nbsp;&nbsp;&nbsp;<?= $subCat['name'] ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="filter-group">
                    <label>Trier par</label>
                    <select id="sort-filter" onchange="updateSortFilter(this.value)">
                        <option value="">En vedette</option>
                        <option value="price_low" <?= $sortBy === 'price_low' ? 'selected' : '' ?>>Prix: croissant</option>
                        <option value="price_high" <?= $sortBy === 'price_high' ? 'selected' : '' ?>>Prix: décroissant</option>
                        <option value="newest" <?= $sortBy === 'newest' ? 'selected' : '' ?>>Nouveautés</option>
                        <option value="name" <?= $sortBy === 'name' ? 'selected' : '' ?>>Nom</option>
                    </select>
                </div>
            </div>
        </section>

        <section class="products-section">
            <?php if (empty($products)): ?>
                <div class="no-products">
                    <p>Aucun produit trouvé.</p>
                </div>
            <?php else: ?>
                <div class="products-grid">
                    <?php foreach ($products as $product): ?>
                        <div class="product-card <?= $product['sale_price'] ? 'sale' : '' ?>">
                            <?php if ($product['sale_price']): ?>
                                <div class="discount-badge">
                                    -<?= round((($product['price'] - $product['sale_price']) / $product['price']) * 100) ?>%
                                </div>
                            <?php endif; ?>

                            <a href="product.php?slug=<?= $product['slug'] ?>">
                                <img src="<?= $product['image'] ?>" alt="<?= $product['name'] ?>">
                                <div class="product-details">
                                    <h3><?= $product['name'] ?></h3>
                                    <p><?= substr($product['description'], 0, 100) . (strlen($product['description']) > 100 ? '...' : '') ?></p>
                                    <div class="price">
                                        <?php if ($product['sale_price']): ?>
                                            <span class="original-price">$<?= number_format($product['price'], 2) ?></span>
                                            <span class="sale-price">$<?= number_format($product['sale_price'], 2) ?></span>
                                        <?php else: ?>
                                            <span>$<?= number_format($product['price'], 2) ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </a>

                            <button class="btn-secondary add-to-cart" data-product-id="<?= $product['id'] ?>">
                                Ajouter au panier
                            </button>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </section>

        <?php if (!empty($saleProducts) && !$categorySlug && !$search): ?>
            <section class="seasonal-collection">
                <h2>Produits en solde</h2>

                <div class="collection-grid">
                    <?php foreach ($saleProducts as $index => $product): ?>
                        <div class="collection-item <?= $index === 0 ? 'large' : '' ?>">
                            <a href="product.php?slug=<?= $product['slug'] ?>">
                                <img src="<?= $product['image'] ?>" alt="<?= $product['name'] ?>">
                                <div class="item-details">
                                    <h3><?= $product['name'] ?></h3>
                                    <p><?= substr($product['description'], 0, 80) . (strlen($product['description']) > 80 ? '...' : '') ?></p>
                                    <div class="price">
                                        <span class="original-price">$<?= number_format($product['price'], 2) ?></span>
                                        <span class="sale-price">$<?= number_format($product['sale_price'], 2) ?></span>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            </section>
        <?php endif; ?>
    </main>

    <?php include 'includes/footer.php'; ?>

    <script src="js/main.js"></script>
    <script>
        function updateSortFilter(value) {
            // Get current URL
            let url = new URL(window.location.href);

            // Update or add sort parameter
            if (value) {
                url.searchParams.set('sort', value);
            } else {
                url.searchParams.delete('sort');
            }

            // Redirect to new URL
            window.location.href = url.toString();
        }

        // Add to cart functionality
        document.addEventListener('DOMContentLoaded', function() {
            const addToCartButtons = document.querySelectorAll('.add-to-cart');

            addToCartButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.getAttribute('data-product-id');

                    // AJAX request to add item to cart
                    fetch('ajax/add_to_cart.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'product_id=' + productId + '&quantity=1'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success message
                            alert('Produit ajouté au panier');

                            // Update cart count
                            const cartCount = document.querySelector('.cart-count');
                            if (cartCount) {
                                cartCount.textContent = data.cart_count;
                            } else {
                                const cartIcon = document.querySelector('.cart-icon');
                                const newCartCount = document.createElement('span');
                                newCartCount.className = 'cart-count';
                                newCartCount.textContent = data.cart_count;
                                cartIcon.appendChild(newCartCount);
                            }
                        } else {
                            alert(data.message || 'Une erreur est survenue');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Une erreur est survenue');
                    });
                });
            });
        });
    </script>
</body>
</html>