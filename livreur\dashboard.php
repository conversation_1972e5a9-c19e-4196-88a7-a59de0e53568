<?php
require_once '../includes/config.php';

// Require livreur access
$auth->requireLivreur();

$orderObj = new Order();
$livreurId = $auth->getUserId();

// Handle order status updates
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'take_order':
                if ($orderObj->assignToLivreur($_POST['order_id'], $livreurId)) {
                    $message = 'Commande prise en charge avec succès.';
                } else {
                    $error = 'Erreur lors de la prise en charge de la commande.';
                }
                break;
                
            case 'mark_delivered':
                if ($orderObj->markAsDelivered($_POST['order_id'], $livreurId)) {
                    $message = 'Commande marquée comme livrée.';
                } else {
                    $error = 'Erreur lors de la mise à jour du statut.';
                }
                break;
        }
    }
}

// Get orders
$pendingOrders = $orderObj->getPending();
$myOrders = $orderObj->getByLivreur($livreurId);
$deliveredOrders = $orderObj->getDeliveredByLivreur($livreurId);

$pageTitle = 'Tableau de bord Livreur';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - <?= SITE_NAME ?></title>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .livreur-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .livreur-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: #f8f8f8;
            padding: 30px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }
        
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .orders-section {
            margin-bottom: 40px;
        }
        
        .orders-grid {
            display: grid;
            gap: 20px;
        }
        
        .order-card {
            background: #fff;
            border: 1px solid #e0e0e0;
            padding: 20px;
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .order-number {
            font-weight: bold;
            font-size: 18px;
        }
        
        .order-status {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-shipped {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status-delivered {
            background: #d4edda;
            color: #155724;
        }
        
        .order-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .detail-item {
            font-size: 14px;
        }
        
        .detail-label {
            font-weight: bold;
            color: #666;
        }
        
        .btn-livreur {
            display: inline-block;
            padding: 10px 20px;
            background: var(--primary-color);
            color: var(--secondary-color);
            text-decoration: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .btn-livreur:hover {
            background: #333;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .message {
            padding: 15px;
            margin-bottom: 20px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .tab {
            padding: 15px 30px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            border-bottom: 3px solid transparent;
        }
        
        .tab.active {
            border-bottom-color: var(--primary-color);
            font-weight: bold;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body class="livreur-page">
    <?php include '../includes/header.php'; ?>

    <main>
        <div class="livreur-container">
            <div class="livreur-header">
                <h1>Tableau de bord Livreur</h1>
                <p>Bienvenue, <?= $auth->getUser()['first_name'] ?></p>
            </div>

            <?php if ($message): ?>
                <div class="message"><?= $message ?></div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="message error"><?= $error ?></div>
            <?php endif; ?>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?= count($pendingOrders) ?></div>
                    <div class="stat-label">Commandes disponibles</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= count($myOrders) ?></div>
                    <div class="stat-label">Mes livraisons</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= count($deliveredOrders) ?></div>
                    <div class="stat-label">Livrées</div>
                </div>
            </div>

            <div class="tabs">
                <button class="tab active" onclick="showTab('available')">Commandes disponibles</button>
                <button class="tab" onclick="showTab('my-orders')">Mes livraisons</button>
                <button class="tab" onclick="showTab('delivered')">Livrées</button>
            </div>

            <!-- Available Orders -->
            <div id="available" class="tab-content active">
                <div class="orders-section">
                    <h2>Commandes disponibles</h2>
                    <?php if (empty($pendingOrders)): ?>
                        <p>Aucune commande disponible pour le moment.</p>
                    <?php else: ?>
                        <div class="orders-grid">
                            <?php foreach ($pendingOrders as $order): ?>
                                <div class="order-card">
                                    <div class="order-header">
                                        <div class="order-number"><?= $order['order_number'] ?></div>
                                        <div class="order-status status-<?= $order['status'] ?>"><?= ucfirst($order['status']) ?></div>
                                    </div>
                                    <div class="order-details">
                                        <div class="detail-item">
                                            <div class="detail-label">Client:</div>
                                            <?= $order['first_name'] ?> <?= $order['last_name'] ?>
                                        </div>
                                        <div class="detail-item">
                                            <div class="detail-label">Total:</div>
                                            <?= number_format($order['total_amount'], 2) ?>€
                                        </div>
                                        <div class="detail-item">
                                            <div class="detail-label">Adresse:</div>
                                            <?= $order['shipping_address'] ?>, <?= $order['shipping_city'] ?>
                                        </div>
                                        <div class="detail-item">
                                            <div class="detail-label">Date:</div>
                                            <?= date('d/m/Y H:i', strtotime($order['created_at'])) ?>
                                        </div>
                                    </div>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="take_order">
                                        <input type="hidden" name="order_id" value="<?= $order['id'] ?>">
                                        <button type="submit" class="btn-livreur">Prendre en charge</button>
                                    </form>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- My Orders -->
            <div id="my-orders" class="tab-content">
                <div class="orders-section">
                    <h2>Mes livraisons en cours</h2>
                    <?php 
                    $myActiveOrders = array_filter($myOrders, function($order) {
                        return $order['status'] !== 'delivered';
                    });
                    ?>
                    <?php if (empty($myActiveOrders)): ?>
                        <p>Aucune livraison en cours.</p>
                    <?php else: ?>
                        <div class="orders-grid">
                            <?php foreach ($myActiveOrders as $order): ?>
                                <div class="order-card">
                                    <div class="order-header">
                                        <div class="order-number"><?= $order['order_number'] ?></div>
                                        <div class="order-status status-<?= $order['status'] ?>"><?= ucfirst($order['status']) ?></div>
                                    </div>
                                    <div class="order-details">
                                        <div class="detail-item">
                                            <div class="detail-label">Client:</div>
                                            <?= $order['first_name'] ?> <?= $order['last_name'] ?>
                                        </div>
                                        <div class="detail-item">
                                            <div class="detail-label">Total:</div>
                                            <?= number_format($order['total_amount'], 2) ?>€
                                        </div>
                                        <div class="detail-item">
                                            <div class="detail-label">Adresse:</div>
                                            <?= $order['shipping_address'] ?>, <?= $order['shipping_city'] ?>
                                        </div>
                                        <div class="detail-item">
                                            <div class="detail-label">Date:</div>
                                            <?= date('d/m/Y H:i', strtotime($order['created_at'])) ?>
                                        </div>
                                    </div>
                                    <?php if ($order['status'] === 'shipped'): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="mark_delivered">
                                            <input type="hidden" name="order_id" value="<?= $order['id'] ?>">
                                            <button type="submit" class="btn-livreur btn-success">Marquer comme livrée</button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Delivered Orders -->
            <div id="delivered" class="tab-content">
                <div class="orders-section">
                    <h2>Commandes livrées</h2>
                    <?php if (empty($deliveredOrders)): ?>
                        <p>Aucune commande livrée.</p>
                    <?php else: ?>
                        <div class="orders-grid">
                            <?php foreach ($deliveredOrders as $order): ?>
                                <div class="order-card">
                                    <div class="order-header">
                                        <div class="order-number"><?= $order['order_number'] ?></div>
                                        <div class="order-status status-<?= $order['status'] ?>"><?= ucfirst($order['status']) ?></div>
                                    </div>
                                    <div class="order-details">
                                        <div class="detail-item">
                                            <div class="detail-label">Client:</div>
                                            <?= $order['first_name'] ?> <?= $order['last_name'] ?>
                                        </div>
                                        <div class="detail-item">
                                            <div class="detail-label">Total:</div>
                                            <?= number_format($order['total_amount'], 2) ?>€
                                        </div>
                                        <div class="detail-item">
                                            <div class="detail-label">Livré le:</div>
                                            <?= $order['delivered_at'] ? date('d/m/Y H:i', strtotime($order['delivered_at'])) : 'N/A' ?>
                                        </div>
                                        <div class="detail-item">
                                            <div class="detail-label">Adresse:</div>
                                            <?= $order['shipping_address'] ?>, <?= $order['shipping_city'] ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <?php include '../includes/footer.php'; ?>
    
    <script src="../js/main.js"></script>
    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
