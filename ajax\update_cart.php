<?php
require_once '../includes/config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Méthode non autorisée'
    ]);
    exit;
}

// Get product ID and quantity
$productId = isset($_POST['product_id']) ? (int)$_POST['product_id'] : 0;
$quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 0;
$action = isset($_POST['action']) ? $_POST['action'] : '';

// Validate product ID
if ($productId <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'ID de produit invalide'
    ]);
    exit;
}

// Initialize cart
$cart = new Cart();

// Perform action based on request
if ($action === 'remove') {
    // Remove item from cart
    $result = $cart->removeItem($productId);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Produit retiré du panier',
            'cart_count' => $cart->getItemCount(),
            'cart_total' => number_format($cart->getTotal(), 2)
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Impossible de retirer le produit du panier'
        ]);
    }
} else {
    // Update item quantity
    if ($quantity <= 0) {
        // If quantity is 0 or negative, remove item
        $result = $cart->removeItem($productId);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Produit retiré du panier',
                'cart_count' => $cart->getItemCount(),
                'cart_total' => number_format($cart->getTotal(), 2)
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Impossible de retirer le produit du panier'
            ]);
        }
    } else {
        // Update quantity
        $result = $cart->updateItem($productId, $quantity);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Quantité mise à jour',
                'cart_count' => $cart->getItemCount(),
                'cart_total' => number_format($cart->getTotal(), 2)
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Impossible de mettre à jour la quantité'
            ]);
        }
    }
}
