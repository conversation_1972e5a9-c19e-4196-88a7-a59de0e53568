<?php
// Include configuration file if not already included
if (!defined('SITE_NAME')) {
    require_once __DIR__ . '/config.php';
}

// Initialize cart
$cart = new Cart();
$cartItemCount = $cart->getItemCount();

// Initialize Product class for categories
$productObj = new Product();
$categoryHierarchy = $productObj->getCategoryHierarchy();

// Determine the base path for links based on current directory
$currentDir = basename(dirname($_SERVER['SCRIPT_NAME']));
$basePath = ($currentDir === 'admin' || $currentDir === 'livreur') ? '../' : '';
?>
<header>
    <div class="header-container">
        <div class="menu-toggle">
            <span></span>
        </div>

        <div class="logo">
            <a href="<?= $basePath ?>index.php">Nuit Blanche</a>
        </div>

        <div class="search-bar">
            <form action="<?= $basePath ?>shop.php" method="GET">
                <input type="text" name="search" placeholder="Rechercher">
                <button type="submit"><i class="search-icon"></i></button>
            </form>
        </div>

        <div class="user-actions">
            <?php if ($auth->isLoggedIn()): ?>
                <?php if ($auth->isAdmin()): ?>
                    <a href="<?= $basePath ?>admin/dashboard.php">admin</a>
                <?php elseif ($auth->isLivreur()): ?>
                    <a href="<?= $basePath ?>livreur/dashboard.php">livreur</a>
                <?php endif; ?>
                <a href="<?= $basePath ?>profile.php">profil</a>
                <a href="<?= $basePath ?>logout.php">déconnexion</a>
            <?php else: ?>
                <a href="<?= $basePath ?>login.php">connexion</a>
                <a href="<?= $basePath ?>register.php">inscription</a>
            <?php endif; ?>
            <a href="<?= $basePath ?>cart.php" class="cart-icon">
                panier
                <?php if ($cartItemCount > 0): ?>
                    <span class="cart-count"><?= $cartItemCount ?></span>
                <?php endif; ?>
            </a>
        </div>
    </div>

    <nav class="main-nav">
        <ul>
            <li><a href="<?= $basePath ?>shop.php">Collection</a></li>
            <?php foreach ($categoryHierarchy as $mainCategory): ?>
                <li class="dropdown">
                    <a href="<?= $basePath ?>shop.php?category=<?= $mainCategory['slug'] ?>"><?= $mainCategory['name'] ?></a>
                    <?php if (!empty($mainCategory['subcategories'])): ?>
                        <div class="dropdown-menu">
                            <ul>
                                <?php foreach ($mainCategory['subcategories'] as $subcategory): ?>
                                    <li><a href="<?= $basePath ?>shop.php?category=<?= $subcategory['slug'] ?>"><?= $subcategory['name'] ?></a></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </li>
            <?php endforeach; ?>
            <li><a href="<?= $basePath ?>about.php">À propos</a></li>
        </ul>
    </nav>
</header>