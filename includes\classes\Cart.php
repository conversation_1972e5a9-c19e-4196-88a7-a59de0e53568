<?php
/**
 * Cart Class
 *
 * Handles shopping cart operations
 */
class Cart {
    private $db;
    private $auth;

    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
        $this->auth = Auth::getInstance();

        // Initialize session cart if not exists
        if (!isset($_SESSION['cart'])) {
            $_SESSION['cart'] = [];
        }
    }

    /**
     * Add item to cart
     *
     * @param int $productId
     * @param int $quantity
     * @return bool
     */
    public function addItem($productId, $quantity = 1) {
        // Get product details
        $product = $this->db->fetchOne(
            "SELECT * FROM products WHERE id = :id",
            ['id' => $productId]
        );

        if (!$product) {
            return false;
        }

        // Check if product is in stock
        if ($product['stock'] < $quantity) {
            return false;
        }

        // If user is logged in, store in database
        if ($this->auth->isLoggedIn()) {
            $userId = $this->auth->getUserId();

            // Check if product already in cart
            $cartItem = $this->db->fetchOne(
                "SELECT * FROM cart_items WHERE user_id = :user_id AND product_id = :product_id",
                ['user_id' => $userId, 'product_id' => $productId]
            );

            if ($cartItem) {
                // Update quantity
                $newQuantity = $cartItem['quantity'] + $quantity;

                // Check stock again
                if ($product['stock'] < $newQuantity) {
                    $newQuantity = $product['stock'];
                }

                return $this->db->update(
                    'cart_items',
                    ['quantity' => $newQuantity],
                    'id = :id',
                    ['id' => $cartItem['id']]
                );
            } else {
                // Insert new cart item
                return $this->db->insert('cart_items', [
                    'user_id' => $userId,
                    'product_id' => $productId,
                    'quantity' => $quantity
                ]) ? true : false;
            }
        } else {
            // Store in session
            if (isset($_SESSION['cart'][$productId])) {
                // Update quantity
                $_SESSION['cart'][$productId] += $quantity;

                // Check stock
                if ($_SESSION['cart'][$productId] > $product['stock']) {
                    $_SESSION['cart'][$productId] = $product['stock'];
                }
            } else {
                // Add new item
                $_SESSION['cart'][$productId] = $quantity;
            }

            return true;
        }
    }

    /**
     * Update cart item quantity
     *
     * @param int $productId
     * @param int $quantity
     * @return bool
     */
    public function updateItem($productId, $quantity) {
        // Validate quantity
        if ($quantity <= 0) {
            return $this->removeItem($productId);
        }

        // Get product details
        $product = $this->db->fetchOne(
            "SELECT * FROM products WHERE id = :id",
            ['id' => $productId]
        );

        if (!$product) {
            return false;
        }

        // Check if product is in stock
        if ($product['stock'] < $quantity) {
            $quantity = $product['stock'];
        }

        // If user is logged in, update in database
        if ($this->auth->isLoggedIn()) {
            $userId = $this->auth->getUserId();

            return $this->db->update(
                'cart_items',
                ['quantity' => $quantity],
                'user_id = :user_id AND product_id = :product_id',
                ['user_id' => $userId, 'product_id' => $productId]
            );
        } else {
            // Update in session
            $_SESSION['cart'][$productId] = $quantity;
            return true;
        }
    }

    /**
     * Remove item from cart
     *
     * @param int $productId
     * @return bool
     */
    public function removeItem($productId) {
        // If user is logged in, remove from database
        if ($this->auth->isLoggedIn()) {
            $userId = $this->auth->getUserId();

            return $this->db->delete(
                'cart_items',
                'user_id = :user_id AND product_id = :product_id',
                ['user_id' => $userId, 'product_id' => $productId]
            );
        } else {
            // Remove from session
            if (isset($_SESSION['cart'][$productId])) {
                unset($_SESSION['cart'][$productId]);
                return true;
            }

            return false;
        }
    }

    /**
     * Clear cart
     *
     * @return bool
     */
    public function clear() {
        // If user is logged in, clear from database
        if ($this->auth->isLoggedIn()) {
            $userId = $this->auth->getUserId();

            return $this->db->delete(
                'cart_items',
                'user_id = :user_id',
                ['user_id' => $userId]
            );
        } else {
            // Clear session cart
            $_SESSION['cart'] = [];
            return true;
        }
    }

    /**
     * Get cart items with product details
     *
     * @return array
     */
    public function getItems() {
        // If user is logged in, get from database
        if ($this->auth->isLoggedIn()) {
            $userId = $this->auth->getUserId();

            return $this->db->fetchAll(
                "SELECT ci.*, p.name, p.slug, p.description, p.price, p.sale_price, p.image
                 FROM cart_items ci
                 JOIN products p ON ci.product_id = p.id
                 WHERE ci.user_id = :user_id",
                ['user_id' => $userId]
            );
        } else {
            // Get from session
            $items = [];

            if (!empty($_SESSION['cart'])) {
                $productIds = array_keys($_SESSION['cart']);

                // Create named placeholders and parameters for the IN clause
                $placeholders = [];
                $params = [];
                foreach ($productIds as $index => $productId) {
                    $placeholder = "id_$index";
                    $placeholders[] = ":$placeholder";
                    $params[$placeholder] = $productId;
                }
                $placeholderString = implode(',', $placeholders);

                $products = $this->db->fetchAll(
                    "SELECT * FROM products WHERE id IN ({$placeholderString})",
                    $params
                );

                if ($products) {
                    foreach ($products as $product) {
                        $items[] = [
                            'product_id' => $product['id'],
                            'quantity' => $_SESSION['cart'][$product['id']],
                            'name' => $product['name'],
                            'slug' => $product['slug'],
                            'description' => $product['description'],
                            'price' => $product['price'],
                            'sale_price' => $product['sale_price'],
                            'image' => $product['image']
                        ];
                    }
                }
            }

            return $items;
        }
    }

    /**
     * Get cart total
     *
     * @return float
     */
    public function getTotal() {
        $items = $this->getItems();
        $total = 0;

        foreach ($items as $item) {
            $price = $item['sale_price'] ? $item['sale_price'] : $item['price'];
            $total += $price * $item['quantity'];
        }

        return $total;
    }

    /**
     * Get cart item count
     *
     * @return int
     */
    public function getItemCount() {
        // If user is logged in, count from database
        if ($this->auth->isLoggedIn()) {
            $userId = $this->auth->getUserId();

            $result = $this->db->fetchOne(
                "SELECT SUM(quantity) as count FROM cart_items WHERE user_id = :user_id",
                ['user_id' => $userId]
            );

            return $result ? (int)$result['count'] : 0;
        } else {
            // Count from session
            $count = 0;

            if (!empty($_SESSION['cart'])) {
                foreach ($_SESSION['cart'] as $quantity) {
                    $count += $quantity;
                }
            }

            return $count;
        }
    }

    /**
     * Sync session cart with database after login
     *
     * @param int $userId
     * @return void
     */
    public function syncWithDatabase($userId) {
        if (empty($_SESSION['cart'])) {
            return;
        }

        foreach ($_SESSION['cart'] as $productId => $quantity) {
            // Check if product already in database cart
            $cartItem = $this->db->fetchOne(
                "SELECT * FROM cart_items WHERE user_id = :user_id AND product_id = :product_id",
                ['user_id' => $userId, 'product_id' => $productId]
            );

            if ($cartItem) {
                // Update quantity
                $this->db->update(
                    'cart_items',
                    ['quantity' => $cartItem['quantity'] + $quantity],
                    'id = :id',
                    ['id' => $cartItem['id']]
                );
            } else {
                // Insert new cart item
                $this->db->insert('cart_items', [
                    'user_id' => $userId,
                    'product_id' => $productId,
                    'quantity' => $quantity
                ]);
            }
        }

        // Clear session cart
        $_SESSION['cart'] = [];
    }
}
