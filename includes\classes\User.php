<?php
/**
 * User Class
 * 
 * Handles user-related operations
 */
class User {
    private $db;
    private $table = 'users';
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Register a new user
     * 
     * @param array $userData
     * @return int|false User ID or false on failure
     */
    public function register($userData) {
        // Check if email already exists
        $existingUser = $this->findByEmail($userData['email']);
        if ($existingUser) {
            return false;
        }
        
        // Hash the password
        $userData['password'] = password_hash($userData['password'], PASSWORD_DEFAULT);
        
        // Insert the user
        return $this->db->insert($this->table, $userData);
    }
    
    /**
     * Login a user
     * 
     * @param string $email
     * @param string $password
     * @return array|false User data or false on failure
     */
    public function login($email, $password) {
        $user = $this->findByEmail($email);
        
        if (!$user) {
            return false;
        }
        
        if (password_verify($password, $user['password'])) {
            // Remove password from user data
            unset($user['password']);
            return $user;
        }
        
        return false;
    }
    
    /**
     * Find a user by email
     * 
     * @param string $email
     * @return array|false User data or false if not found
     */
    public function findByEmail($email) {
        return $this->db->fetchOne(
            "SELECT * FROM {$this->table} WHERE email = :email",
            ['email' => $email]
        );
    }
    
    /**
     * Find a user by ID
     * 
     * @param int $id
     * @return array|false User data or false if not found
     */
    public function findById($id) {
        return $this->db->fetchOne(
            "SELECT * FROM {$this->table} WHERE id = :id",
            ['id' => $id]
        );
    }
    
    /**
     * Update user profile
     * 
     * @param int $userId
     * @param array $userData
     * @return bool
     */
    public function updateProfile($userId, $userData) {
        // Don't allow email to be updated here
        if (isset($userData['email'])) {
            unset($userData['email']);
        }
        
        return $this->db->update(
            $this->table,
            $userData,
            'id = :id',
            ['id' => $userId]
        );
    }
    
    /**
     * Change user password
     * 
     * @param int $userId
     * @param string $currentPassword
     * @param string $newPassword
     * @return bool
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        $user = $this->findById($userId);
        
        if (!$user) {
            return false;
        }
        
        if (!password_verify($currentPassword, $user['password'])) {
            return false;
        }
        
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        return $this->db->update(
            $this->table,
            ['password' => $hashedPassword],
            'id = :id',
            ['id' => $userId]
        );
    }
    
    /**
     * Get user addresses
     * 
     * @param int $userId
     * @return array
     */
    public function getAddresses($userId) {
        return $this->db->fetchAll(
            "SELECT * FROM addresses WHERE user_id = :user_id",
            ['user_id' => $userId]
        );
    }
    
    /**
     * Add a new address
     * 
     * @param array $addressData
     * @return int|false Address ID or false on failure
     */
    public function addAddress($addressData) {
        return $this->db->insert('addresses', $addressData);
    }
    
    /**
     * Update an address
     * 
     * @param int $addressId
     * @param int $userId
     * @param array $addressData
     * @return bool
     */
    public function updateAddress($addressId, $userId, $addressData) {
        return $this->db->update(
            'addresses',
            $addressData,
            'id = :id AND user_id = :user_id',
            ['id' => $addressId, 'user_id' => $userId]
        );
    }
    
    /**
     * Delete an address
     * 
     * @param int $addressId
     * @param int $userId
     * @return bool
     */
    public function deleteAddress($addressId, $userId) {
        return $this->db->delete(
            'addresses',
            'id = :id AND user_id = :user_id',
            ['id' => $addressId, 'user_id' => $userId]
        );
    }
}
