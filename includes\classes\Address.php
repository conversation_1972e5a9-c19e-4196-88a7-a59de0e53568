<?php
/**
 * Address Class
 * 
 * Handles address-related operations
 */
class Address {
    private $db;
    private $table = 'addresses';
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get all addresses for a user
     * 
     * @param int $userId
     * @return array
     */
    public function getUserAddresses($userId) {
        return $this->db->fetchAll(
            "SELECT * FROM {$this->table} WHERE user_id = :user_id ORDER BY is_default_shipping DESC, is_default_billing DESC, created_at DESC",
            ['user_id' => $userId]
        );
    }
    
    /**
     * Get a specific address by ID
     * 
     * @param int $addressId
     * @param int $userId
     * @return array|false
     */
    public function getAddress($addressId, $userId) {
        return $this->db->fetchOne(
            "SELECT * FROM {$this->table} WHERE id = :id AND user_id = :user_id",
            ['id' => $addressId, 'user_id' => $userId]
        );
    }
    
    /**
     * Add a new address
     * 
     * @param array $addressData
     * @return int|false Address ID or false on failure
     */
    public function addAddress($addressData) {
        // If this is set as default shipping, unset others
        if (isset($addressData['is_default_shipping']) && $addressData['is_default_shipping']) {
            $this->db->update(
                $this->table,
                ['is_default_shipping' => 0],
                'user_id = :user_id',
                ['user_id' => $addressData['user_id']]
            );
        }
        
        // If this is set as default billing, unset others
        if (isset($addressData['is_default_billing']) && $addressData['is_default_billing']) {
            $this->db->update(
                $this->table,
                ['is_default_billing' => 0],
                'user_id = :user_id',
                ['user_id' => $addressData['user_id']]
            );
        }
        
        return $this->db->insert($this->table, $addressData);
    }
    
    /**
     * Update an address
     * 
     * @param int $addressId
     * @param int $userId
     * @param array $addressData
     * @return bool
     */
    public function updateAddress($addressId, $userId, $addressData) {
        // If this is set as default shipping, unset others
        if (isset($addressData['is_default_shipping']) && $addressData['is_default_shipping']) {
            $this->db->update(
                $this->table,
                ['is_default_shipping' => 0],
                'user_id = :user_id',
                ['user_id' => $userId]
            );
        }
        
        // If this is set as default billing, unset others
        if (isset($addressData['is_default_billing']) && $addressData['is_default_billing']) {
            $this->db->update(
                $this->table,
                ['is_default_billing' => 0],
                'user_id = :user_id',
                ['user_id' => $userId]
            );
        }
        
        return $this->db->update(
            $this->table,
            $addressData,
            'id = :id AND user_id = :user_id',
            ['id' => $addressId, 'user_id' => $userId]
        );
    }
    
    /**
     * Delete an address
     * 
     * @param int $addressId
     * @param int $userId
     * @return bool
     */
    public function deleteAddress($addressId, $userId) {
        return $this->db->delete(
            $this->table,
            'id = :id AND user_id = :user_id',
            ['id' => $addressId, 'user_id' => $userId]
        );
    }
    
    /**
     * Get default shipping address
     * 
     * @param int $userId
     * @return array|false
     */
    public function getDefaultShippingAddress($userId) {
        return $this->db->fetchOne(
            "SELECT * FROM {$this->table} WHERE user_id = :user_id AND is_default_shipping = 1",
            ['user_id' => $userId]
        );
    }
    
    /**
     * Get default billing address
     * 
     * @param int $userId
     * @return array|false
     */
    public function getDefaultBillingAddress($userId) {
        return $this->db->fetchOne(
            "SELECT * FROM {$this->table} WHERE user_id = :user_id AND is_default_billing = 1",
            ['user_id' => $userId]
        );
    }
    
    /**
     * Format address for display
     * 
     * @param array $address
     * @return string
     */
    public function formatAddress($address) {
        $formatted = '';
        if (!empty($address['address_line1'])) {
            $formatted .= $address['address_line1'];
        }
        if (!empty($address['address_line2'])) {
            $formatted .= ', ' . $address['address_line2'];
        }
        if (!empty($address['city'])) {
            $formatted .= ', ' . $address['city'];
        }
        if (!empty($address['postal_code'])) {
            $formatted .= ' ' . $address['postal_code'];
        }
        if (!empty($address['country'])) {
            $formatted .= ', ' . $address['country'];
        }
        
        return $formatted;
    }
}
