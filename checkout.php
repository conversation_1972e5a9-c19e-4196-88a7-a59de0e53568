<?php
require_once 'includes/config.php';

// Require login for checkout
$auth->requireLogin('login.php?redirect=checkout.php');

// Initialize cart
$cart = new Cart();
$cartItems = $cart->getItems();
$cartTotal = $cart->getTotal();

// Fixed shipping cost
$shippingCost = 15.00;

// If cart is empty, redirect to cart page
if (empty($cartItems)) {
    header('Location: cart.php');
    exit;
}

// Get user data
$userId = $auth->getUserId();
$user = new User();
$userData = $user->findById($userId);
$userAddresses = $user->getAddresses($userId);

// Process checkout form
$success = false;
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate form data
    $shippingAddressId = isset($_POST['shipping_address']) ? (int)$_POST['shipping_address'] : 0;
    $billingAddressId = isset($_POST['billing_address']) ? (int)$_POST['billing_address'] : 0;
    $paymentMethod = isset($_POST['payment_method']) ? $_POST['payment_method'] : '';

    if ($shippingAddressId <= 0) {
        $error = 'Veuillez sélectionner une adresse de livraison.';
    } elseif ($billingAddressId <= 0) {
        $error = 'Veuillez sélectionner une adresse de facturation.';
    } elseif (empty($paymentMethod)) {
        $error = 'Veuillez sélectionner un mode de paiement.';
    } else {
        // Handle same address checkbox
        if (isset($_POST['same_address'])) {
            $billingAddressId = $shippingAddressId;
        }

        // Create order in database
        $orderClass = new Order();

        // Prepare order data
        $orderData = [
            'user_id' => $userId,
            'status' => 'pending',
            'total_amount' => $cartTotal + $shippingCost,
            'shipping_address_id' => $shippingAddressId,
            'billing_address_id' => $billingAddressId,
            'shipping_method' => 'standard',
            'shipping_cost' => $shippingCost,
            'payment_method' => $paymentMethod,
            'notes' => isset($_POST['notes']) ? $_POST['notes'] : null
        ];

        // Create the order
        $orderId = $orderClass->create($orderData);

        if ($orderId) {
            // Add order items
            $orderItemsSuccess = true;

            foreach ($cartItems as $item) {
                $itemPrice = $item['sale_price'] ?: $item['price'];

                $orderItemData = [
                    'order_id' => $orderId,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'price' => $itemPrice
                ];

                $itemResult = $orderClass->addOrderItem($orderItemData);
                if (!$itemResult) {
                    $orderItemsSuccess = false;
                    break;
                }
            }

            if ($orderItemsSuccess) {
                // Update product stock
                foreach ($cartItems as $item) {
                    $productClass = new Product();
                    $productClass->updateStock($item['product_id'], -$item['quantity']);
                }

                // Clear the cart
                $cart->clear();

                // Set success flag
                $success = true;
                $orderNumber = $orderClass->getOrderNumber($orderId);
            } else {
                // If order items failed, delete the order
                $orderClass->delete($orderId);
                $error = 'Erreur lors de la création des articles de commande.';
            }
        } else {
            $error = 'Erreur lors de la création de la commande.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paiement - <?= SITE_NAME ?></title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="checkout-page">
    <?php include 'includes/header.php'; ?>

    <main>
        <?php if ($success): ?>
            <section class="checkout-success">
                <h1>Commande confirmée</h1>
                <div class="success-details">
                    <p><strong>Numéro de commande:</strong> <?php echo htmlspecialchars($orderNumber); ?></p>
                    <p>Merci pour votre commande ! Nous vous avons envoyé un email de confirmation.</p>
                    <p>Votre commande sera traitée dans les plus brefs délais.</p>
                </div>
                <div class="success-actions">
                    <a href="profile.php?tab=orders" class="btn">Voir mes commandes</a>
                    <a href="shop.php" class="btn-secondary">Continuer vos achats</a>
                </div>
            </section>
        <?php else: ?>
            <section class="checkout-section">
                <h1>Paiement</h1>

                <?php if ($error): ?>
                    <div class="error-message"><?= $error ?></div>
                <?php endif; ?>

                <div class="checkout-container">
                    <form class="checkout-form" method="POST" action="">
                        <div class="checkout-steps">
                            <div class="checkout-step">
                                <h2>1. Adresse de livraison</h2>

                                <?php if (empty($userAddresses)): ?>
                                    <div class="no-addresses">
                                        <p>Vous n'avez pas encore d'adresse enregistrée.</p>
                                        <a href="profile.php?tab=addresses" class="btn-secondary">Ajouter une adresse</a>
                                    </div>
                                <?php else: ?>
                                    <div class="address-selection">
                                        <?php foreach ($userAddresses as $address): ?>
                                            <div class="address-option">
                                                <input type="radio" name="shipping_address" id="shipping_<?= $address['id'] ?>" value="<?= $address['id'] ?>" <?= $address['is_default_shipping'] ? 'checked' : '' ?>>
                                                <label for="shipping_<?= $address['id'] ?>">
                                                    <div class="address-details">
                                                        <p><strong><?= $userData['first_name'] ?> <?= $userData['last_name'] ?></strong></p>
                                                        <p><?= $address['address_line1'] ?></p>
                                                        <?php if ($address['address_line2']): ?>
                                                            <p><?= $address['address_line2'] ?></p>
                                                        <?php endif; ?>
                                                        <p><?= $address['postal_code'] ?> <?= $address['city'] ?></p>
                                                        <p><?= $address['country'] ?></p>
                                                        <p><?= $address['phone'] ?></p>
                                                    </div>
                                                </label>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="checkout-step">
                                <h2>2. Adresse de facturation</h2>

                                <div class="same-address">
                                    <input type="checkbox" id="same_address" checked>
                                    <label for="same_address">Utiliser la même adresse que pour la livraison</label>
                                </div>

                                <div class="address-selection billing-address" style="display: none;">
                                    <?php foreach ($userAddresses as $address): ?>
                                        <div class="address-option">
                                            <input type="radio" name="billing_address" id="billing_<?= $address['id'] ?>" value="<?= $address['id'] ?>" <?= $address['is_default_billing'] ? 'checked' : '' ?>>
                                            <label for="billing_<?= $address['id'] ?>">
                                                <div class="address-details">
                                                    <p><strong><?= $userData['first_name'] ?> <?= $userData['last_name'] ?></strong></p>
                                                    <p><?= $address['address_line1'] ?></p>
                                                    <?php if ($address['address_line2']): ?>
                                                        <p><?= $address['address_line2'] ?></p>
                                                    <?php endif; ?>
                                                    <p><?= $address['postal_code'] ?> <?= $address['city'] ?></p>
                                                    <p><?= $address['country'] ?></p>
                                                    <p><?= $address['phone'] ?></p>
                                                </div>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <div class="checkout-step">
                                <h2>3. Mode de paiement</h2>

                                <div class="payment-methods">
                                    <div class="payment-method">
                                        <input type="radio" name="payment_method" id="payment_card" value="card" checked>
                                        <label for="payment_card">Carte bancaire</label>
                                    </div>

                                    <div class="payment-method">
                                        <input type="radio" name="payment_method" id="payment_paypal" value="paypal">
                                        <label for="payment_paypal">PayPal</label>
                                    </div>
                                </div>

                                <div class="payment-details card-payment">
                                    <div class="form-group">
                                        <label for="card_number">Numéro de carte</label>
                                        <input type="text" id="card_number" placeholder="1234 5678 9012 3456">
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="card_expiry">Date d'expiration</label>
                                            <input type="text" id="card_expiry" placeholder="MM/AA">
                                        </div>

                                        <div class="form-group">
                                            <label for="card_cvv">CVV</label>
                                            <input type="text" id="card_cvv" placeholder="123">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="card_name">Nom sur la carte</label>
                                        <input type="text" id="card_name" placeholder="John Doe">
                                    </div>
                                </div>

                                <div class="payment-details paypal-payment" style="display: none;">
                                    <p>Vous serez redirigé vers PayPal pour finaliser votre paiement.</p>
                                </div>
                            </div>
                        </div>

                        <div class="checkout-summary">
                            <h2>Récapitulatif</h2>

                            <div class="cart-items-summary">
                                <?php foreach ($cartItems as $item): ?>
                                    <div class="cart-item-summary">
                                        <div class="item-image">
                                            <img src="<?= $item['image'] ?>" alt="<?= $item['name'] ?>">
                                        </div>
                                        <div class="item-details">
                                            <h3><?= $item['name'] ?></h3>
                                            <p>Quantité: <?= $item['quantity'] ?></p>
                                            <p>
                                                <?php if ($item['sale_price']): ?>
                                                    <span class="original-price">$<?= number_format($item['price'], 2) ?></span>
                                                    <span class="sale-price">$<?= number_format($item['sale_price'], 2) ?></span>
                                                <?php else: ?>
                                                    $<?= number_format($item['price'], 2) ?>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <div class="summary-totals">
                                <div class="summary-row">
                                    <span>Sous-total</span>
                                    <span>$<?= number_format($cartTotal, 2) ?></span>
                                </div>

                                <div class="summary-row">
                                    <span>Livraison</span>
                                    <span>$<?= number_format($shippingCost, 2) ?></span>
                                </div>

                                <div class="summary-row total">
                                    <span>Total</span>
                                    <span>$<?= number_format($cartTotal + $shippingCost, 2) ?></span>
                                </div>
                            </div>

                            <button type="submit" class="btn checkout-btn">Confirmer la commande</button>
                        </div>
                    </form>
                </div>
            </section>
        <?php endif; ?>
    </main>

    <?php include 'includes/footer.php'; ?>

    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Same address checkbox
            const sameAddressCheckbox = document.getElementById('same_address');
            const billingAddressSection = document.querySelector('.billing-address');

            if (sameAddressCheckbox && billingAddressSection) {
                sameAddressCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        billingAddressSection.style.display = 'none';

                        // Copy shipping address to billing address
                        const shippingAddress = document.querySelector('input[name="shipping_address"]:checked');
                        if (shippingAddress) {
                            const billingAddressId = shippingAddress.value;
                            const billingAddress = document.querySelector(`input[name="billing_address"][value="${billingAddressId}"]`);
                            if (billingAddress) {
                                billingAddress.checked = true;
                            }
                        }
                    } else {
                        billingAddressSection.style.display = 'block';
                    }
                });
            }

            // Payment method selection
            const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
            const cardPaymentDetails = document.querySelector('.card-payment');
            const paypalPaymentDetails = document.querySelector('.paypal-payment');

            if (paymentMethods.length && cardPaymentDetails && paypalPaymentDetails) {
                paymentMethods.forEach(method => {
                    method.addEventListener('change', function() {
                        if (this.value === 'card') {
                            cardPaymentDetails.style.display = 'block';
                            paypalPaymentDetails.style.display = 'none';
                        } else if (this.value === 'paypal') {
                            cardPaymentDetails.style.display = 'none';
                            paypalPaymentDetails.style.display = 'block';
                        }
                    });
                });
            }
        });
    </script>
</body>
</html>
