<?php
require_once '../includes/config.php';

// Require admin access
$auth->requireAdmin();

$orderObj = new Order();
$message = '';
$error = '';

// Handle order status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'update_status') {
        if ($orderObj->updateStatus($_POST['order_id'], $_POST['status'])) {
            $message = 'Statut de la commande mis à jour avec succès.';
        } else {
            $error = 'Erreur lors de la mise à jour du statut.';
        }
    }
}

// Get all orders
$orders = $orderObj->getAll();

$pageTitle = 'Gestion des Commandes';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - <?= SITE_NAME ?></title>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .btn-admin {
            display: inline-block;
            padding: 12px 24px;
            background: var(--primary-color);
            color: var(--secondary-color);
            text-decoration: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .btn-admin:hover {
            background: #333;
        }
        
        .message {
            padding: 15px;
            margin-bottom: 20px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .orders-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 40px;
            font-size: 14px;
        }
        
        .orders-table th,
        .orders-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .orders-table th {
            background: #f8f8f8;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        
        .order-status {
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: bold;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-processing {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-shipped {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status-delivered {
            background: #d4edda;
            color: #155724;
        }
        
        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-select {
            padding: 4px;
            border: 1px solid #ddd;
            font-size: 12px;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 11px;
            margin-left: 5px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #f8f8f8;
            padding: 20px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .table-container {
            overflow-x: auto;
        }
    </style>
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>

    <main>
        <div class="admin-container">
            <div class="admin-header">
                <h1><?= $pageTitle ?></h1>
                <a href="dashboard.php" class="btn-admin">Retour au tableau de bord</a>
            </div>

            <?php if ($message): ?>
                <div class="message"><?= $message ?></div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="message error"><?= $error ?></div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="stats-grid">
                <?php
                $statusCounts = [
                    'pending' => 0,
                    'processing' => 0,
                    'shipped' => 0,
                    'delivered' => 0,
                    'cancelled' => 0
                ];
                
                foreach ($orders as $order) {
                    $statusCounts[$order['status']]++;
                }
                ?>
                <div class="stat-card">
                    <div class="stat-number"><?= $statusCounts['pending'] ?></div>
                    <div class="stat-label">En attente</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $statusCounts['processing'] ?></div>
                    <div class="stat-label">En traitement</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $statusCounts['shipped'] ?></div>
                    <div class="stat-label">Expédiées</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $statusCounts['delivered'] ?></div>
                    <div class="stat-label">Livrées</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= count($orders) ?></div>
                    <div class="stat-label">Total</div>
                </div>
            </div>

            <!-- Orders Table -->
            <div class="table-container">
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th>N° Commande</th>
                            <th>Client</th>
                            <th>Email</th>
                            <th>Total</th>
                            <th>Statut</th>
                            <th>Livreur</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($orders as $order): ?>
                            <tr>
                                <td><strong><?= $order['order_number'] ?></strong></td>
                                <td><?= $order['first_name'] ?> <?= $order['last_name'] ?></td>
                                <td><?= $order['email'] ?></td>
                                <td><?= number_format($order['total_amount'], 2) ?>€</td>
                                <td>
                                    <span class="order-status status-<?= $order['status'] ?>">
                                        <?= ucfirst($order['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($order['livreur_first_name']): ?>
                                        <?= $order['livreur_first_name'] ?> <?= $order['livreur_last_name'] ?>
                                    <?php else: ?>
                                        <em>Non assigné</em>
                                    <?php endif; ?>
                                </td>
                                <td><?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></td>
                                <td>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="update_status">
                                        <input type="hidden" name="order_id" value="<?= $order['id'] ?>">
                                        <select name="status" class="status-select" onchange="this.form.submit()">
                                            <option value="pending" <?= $order['status'] === 'pending' ? 'selected' : '' ?>>En attente</option>
                                            <option value="processing" <?= $order['status'] === 'processing' ? 'selected' : '' ?>>En traitement</option>
                                            <option value="shipped" <?= $order['status'] === 'shipped' ? 'selected' : '' ?>>Expédiée</option>
                                            <option value="delivered" <?= $order['status'] === 'delivered' ? 'selected' : '' ?>>Livrée</option>
                                            <option value="cancelled" <?= $order['status'] === 'cancelled' ? 'selected' : '' ?>>Annulée</option>
                                        </select>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <?php if (empty($orders)): ?>
                <div style="text-align: center; padding: 40px; color: #666;">
                    <h3>Aucune commande trouvée</h3>
                    <p>Les commandes apparaîtront ici une fois que les clients auront passé des commandes.</p>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <?php include '../includes/footer.php'; ?>
    
    <script src="../js/main.js"></script>
</body>
</html>
