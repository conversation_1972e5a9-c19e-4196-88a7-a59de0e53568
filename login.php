<?php
require_once 'includes/config.php';

// Redirect if already logged in
$auth->redirectIfLoggedIn();

$error = '';
$email = '';

// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        $error = 'Veuillez remplir tous les champs.';
    } else {
        $user = new User();
        $loggedInUser = $user->login($email, $password);
        
        if ($loggedInUser) {
            // Set user session
            $auth->setUser($loggedInUser);
            
            // Sync cart
            $cart = new Cart();
            $cart->syncWithDatabase($loggedInUser['id']);
            
            // Redirect to home page
            header('Location: index.php');
            exit;
        } else {
            $error = 'Email ou mot de passe incorrect.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - <?= SITE_NAME ?></title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="login-page">
    <?php include 'includes/header.php'; ?>
    
    <main>
        <section class="auth-section">
            <h1>Connexion</h1>
            
            <?php if ($error): ?>
                <div class="error-message"><?= $error ?></div>
            <?php endif; ?>
            
            <form class="auth-form" method="POST" action="">
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" value="<?= htmlspecialchars($email) ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Mot de passe</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn">Se connecter</button>
                </div>
                
                <div class="auth-links">
                    <p>Vous n'avez pas de compte ? <a href="register.php">Créer un compte</a></p>
                    <p><a href="forgot-password.php">Mot de passe oublié ?</a></p>
                </div>
            </form>
        </section>
    </main>
    
    <?php include 'includes/footer.php'; ?>
    
    <script src="js/main.js"></script>
</body>
</html>
