<?php
require_once '../includes/config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if request method is GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'success' => false,
        'message' => 'Méthode non autorisée'
    ]);
    exit;
}

// Get product ID
$productId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Validate product ID
if ($productId <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'ID de produit invalide'
    ]);
    exit;
}

// Initialize product class
$productClass = new Product();

// Get product data
$product = $productClass->findById($productId);

if ($product) {
    echo json_encode([
        'success' => true,
        'product' => $product
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Produit non trouvé'
    ]);
}
?>
