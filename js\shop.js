/**
 * Nuit Blanche - Shop Page JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Collection item hover effect
    const collectionItems = document.querySelectorAll('.collection-item');

    collectionItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.querySelector('img').style.transform = 'scale(1.05)';
        });

        item.addEventListener('mouseleave', function() {
            this.querySelector('img').style.transform = 'scale(1)';
        });
    });

    // Filter functionality (placeholder)
    const filterButtons = document.querySelectorAll('.filter-button');
    if (filterButtons.length > 0) {
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const filter = this.dataset.filter;
                console.log(`Filter selected: ${filter}`);
                // Implement actual filtering logic here
            });
        });
    }

    // Quick view functionality
    const quickViewButtons = document.querySelectorAll('.quick-view-button');
    if (quickViewButtons.length > 0) {
        quickViewButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.dataset.productId;
                const productSlug = this.dataset.productSlug;

                if (productId) {
                    openQuickViewModal(productId);
                } else if (productSlug) {
                    // If we have a slug, redirect to product page
                    window.location.href = `product.php?slug=${productSlug}`;
                } else {
                    console.log('Quick view: No product ID or slug found');
                    alert('Impossible d\'afficher l\'aperçu de ce produit');
                }
            });
        });
    }

    // Function to open quick view modal
    function openQuickViewModal(productId) {
        // Show loading state
        const loadingHTML = `
            <div id="quick-view-modal" class="modal" style="display: block;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Aperçu rapide</h3>
                        <span class="close" onclick="closeQuickViewModal()">&times;</span>
                    </div>
                    <div style="padding: 40px; text-align: center;">
                        <p>Chargement...</p>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', loadingHTML);

        // Fetch product data
        fetch(`ajax/get_product.php?id=${productId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateQuickViewModal(data.product);
                } else {
                    showQuickViewError('Produit non trouvé');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showQuickViewError('Erreur lors du chargement du produit');
            });
    }

    // Function to update quick view modal with product data
    function updateQuickViewModal(product) {
        const modal = document.getElementById('quick-view-modal');
        if (!modal) return;

        const modalContent = `
            <div class="modal-content quick-view-content">
                <div class="modal-header">
                    <h3>Aperçu rapide</h3>
                    <span class="close" onclick="closeQuickViewModal()">&times;</span>
                </div>
                <div class="quick-view-body">
                    <div class="quick-view-image">
                        <img src="${product.ImageFile ? 'image.php?id=' + product.id : (product.image || 'images/products/fabric frame nuit blanche.jpg')}" alt="${product.name}">
                    </div>
                    <div class="quick-view-details">
                        <h2>${product.name}</h2>
                        <div class="price">
                            ${product.sale_price ?
                                `<span class="sale-price">${product.sale_price}€</span>
                                 <span class="original-price">${product.price}€</span>` :
                                `${product.price}€`
                            }
                        </div>
                        <p class="description">${product.description || 'Aucune description disponible'}</p>
                        <div class="stock-status ${product.stock > 0 ? 'in-stock' : 'out-of-stock'}">
                            ${product.stock > 0 ?
                                `En stock (${product.stock} disponible${product.stock > 1 ? 's' : ''})` :
                                'Rupture de stock'
                            }
                        </div>
                        <div class="quick-view-actions">
                            ${product.stock > 0 ?
                                `<button class="btn" onclick="addToCartFromQuickView(${product.id})">Ajouter au panier</button>` :
                                `<button class="btn" disabled>Rupture de stock</button>`
                            }
                            <a href="product.php?slug=${product.slug}" class="btn-secondary">Voir le produit</a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        modal.innerHTML = modalContent;
    }

    // Function to show error in quick view modal
    function showQuickViewError(message) {
        const modal = document.getElementById('quick-view-modal');
        if (!modal) return;

        const errorContent = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Aperçu rapide</h3>
                    <span class="close" onclick="closeQuickViewModal()">&times;</span>
                </div>
                <div style="padding: 40px; text-align: center;">
                    <p>${message}</p>
                    <button class="btn-secondary" onclick="closeQuickViewModal()">Fermer</button>
                </div>
            </div>
        `;

        modal.innerHTML = errorContent;
    }

    // Function to close quick view modal
    window.closeQuickViewModal = function() {
        const modal = document.getElementById('quick-view-modal');
        if (modal) {
            modal.remove();
        }
    }

    // Function to add to cart from quick view
    window.addToCartFromQuickView = function(productId) {
        fetch('ajax/add_to_cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `product_id=${productId}&quantity=1`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Produit ajouté au panier avec succès!');

                // Update cart count if function exists
                if (typeof updateCartCount === 'function') {
                    updateCartCount(data.cart_count);
                }

                // Close modal
                closeQuickViewModal();
            } else {
                alert(data.message || 'Erreur lors de l\'ajout au panier');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de l\'ajout au panier');
        });
    }

    // Close modal when clicking outside
    document.addEventListener('click', function(event) {
        const modal = document.getElementById('quick-view-modal');
        if (modal && event.target === modal) {
            closeQuickViewModal();
        }
    });
});
