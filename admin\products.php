<?php
require_once '../includes/config.php';

// Require admin access
$auth->requireAdmin();

$productObj = new Product();
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                try {
                    $productData = [
                        'name' => $_POST['name'],
                        'slug' => strtolower(str_replace(' ', '-', $_POST['name'])),
                        'description' => $_POST['description'],
                        'price' => $_POST['price'],
                        'sale_price' => !empty($_POST['sale_price']) ? $_POST['sale_price'] : null,
                        'image' => $_POST['image'],
                        'category_id' => $_POST['category_id'],
                        'stock' => $_POST['stock'],
                        'is_featured' => isset($_POST['is_featured']) ? 1 : 0,
                        'ImageFile' => ''
                    ];

                    // Handle file upload if provided
                    if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
                        $imageBlob = $productObj->handleImageUpload($_FILES['image_file']);
                        if ($imageBlob !== false) {
                            $productData['ImageFile'] = $imageBlob;
                        }
                    }

                    if ($productObj->create($productData)) {
                        $message = 'Produit ajouté avec succès.';
                    } else {
                        $error = 'Erreur lors de l\'ajout du produit.';
                    }
                } catch (Exception $e) {
                    $error = 'Erreur lors de l\'upload de l\'image: ' . $e->getMessage();
                }
                break;

            case 'edit':
                try {
                    $productData = [
                        'name' => $_POST['name'],
                        'description' => $_POST['description'],
                        'price' => $_POST['price'],
                        'sale_price' => !empty($_POST['sale_price']) ? $_POST['sale_price'] : null,
                        'image' => $_POST['image'],
                        'category_id' => $_POST['category_id'],
                        'stock' => $_POST['stock'],
                        'is_featured' => isset($_POST['is_featured']) ? 1 : 0
                    ];

                    // Handle file upload if provided
                    if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
                        $imageBlob = $productObj->handleImageUpload($_FILES['image_file']);
                        if ($imageBlob !== false) {
                            $productData['ImageFile'] = $imageBlob;
                        }
                    }

                    if ($productObj->update($_POST['product_id'], $productData)) {
                        $message = 'Produit modifié avec succès.';
                    } else {
                        $error = 'Erreur lors de la modification du produit.';
                    }
                } catch (Exception $e) {
                    $error = 'Erreur lors de l\'upload de l\'image: ' . $e->getMessage();
                }
                break;

            case 'delete':
                if ($productObj->delete($_POST['product_id'])) {
                    $message = 'Produit supprimé avec succès.';
                } else {
                    $error = 'Erreur lors de la suppression du produit.';
                }
                break;
        }
    }
}

// Get all products and categories
$products = $productObj->getAll();
$categories = $productObj->getAllCategories();

// Get product for editing if specified
$editProduct = null;
if (isset($_GET['edit'])) {
    $editProduct = $productObj->getById($_GET['edit']);
}

$pageTitle = 'Gestion des Produits';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - <?= SITE_NAME ?></title>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .btn-admin {
            display: inline-block;
            padding: 12px 24px;
            background: var(--primary-color);
            color: var(--secondary-color);
            text-decoration: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-admin:hover {
            background: #333;
        }

        .btn-small {
            padding: 8px 16px;
            font-size: 12px;
            margin-right: 5px;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .message {
            padding: 15px;
            margin-bottom: 20px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 40px;
        }

        .products-table th,
        .products-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .products-table th {
            background: #f8f8f8;
            font-weight: bold;
        }

        .product-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
        }

        .form-container {
            background: #f8f8f8;
            padding: 30px;
            margin-bottom: 40px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            font-family: inherit;
        }

        .form-group textarea {
            height: 100px;
            resize: vertical;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .full-width {
            grid-column: 1 / -1;
        }
    </style>
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>

    <main>
        <div class="admin-container">
            <div class="admin-header">
                <h1><?= $pageTitle ?></h1>
                <a href="dashboard.php" class="btn-admin">Retour au tableau de bord</a>
            </div>

            <?php if ($message): ?>
                <div class="message"><?= $message ?></div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="message error"><?= $error ?></div>
            <?php endif; ?>

            <!-- Product Form -->
            <div class="form-container">
                <h2><?= $editProduct ? 'Modifier le produit' : 'Ajouter un nouveau produit' ?></h2>
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="<?= $editProduct ? 'edit' : 'add' ?>">
                    <?php if ($editProduct): ?>
                        <input type="hidden" name="product_id" value="<?= $editProduct['id'] ?>">
                    <?php endif; ?>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="name">Nom du produit</label>
                            <input type="text" id="name" name="name" value="<?= $editProduct['name'] ?? '' ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="category_id">Catégorie</label>
                            <select id="category_id" name="category_id" required>
                                <option value="">Sélectionner une catégorie</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>" <?= ($editProduct && $editProduct['category_id'] == $category['id']) ? 'selected' : '' ?>>
                                        <?= $category['name'] ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="price">Prix (€)</label>
                            <input type="number" step="0.01" id="price" name="price" value="<?= $editProduct['price'] ?? '' ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="sale_price">Prix soldé (€)</label>
                            <input type="number" step="0.01" id="sale_price" name="sale_price" value="<?= $editProduct['sale_price'] ?? '' ?>">
                        </div>

                        <div class="form-group">
                            <label for="stock">Stock</label>
                            <input type="number" id="stock" name="stock" value="<?= $editProduct['stock'] ?? '' ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="image_file">Image (fichier)</label>
                            <input type="file" id="image_file" name="image_file" accept="image/*">
                            <small>Formats acceptés: JPEG, PNG, GIF, WebP. Taille max: 5MB</small>
                            <?php if ($editProduct && !empty($editProduct['ImageFile'])): ?>
                                <div style="margin-top: 10px;">
                                    <img src="../image.php?id=<?= $editProduct['id'] ?>" alt="Image actuelle" style="max-width: 100px; max-height: 100px;">
                                    <p><small>Image actuelle (stockée en base)</small></p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="form-group">
                            <label for="image">Image (chemin alternatif)</label>
                            <input type="text" id="image" name="image" value="<?= $editProduct['image'] ?? '' ?>" placeholder="images/products/nom-image.jpg">
                            <small>Utilisé uniquement si aucun fichier n'est uploadé</small>
                        </div>

                        <div class="form-group full-width">
                            <label for="description">Description</label>
                            <textarea id="description" name="description" required><?= $editProduct['description'] ?? '' ?></textarea>
                        </div>

                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="is_featured" name="is_featured" <?= ($editProduct && $editProduct['is_featured']) ? 'checked' : '' ?>>
                                <label for="is_featured">Produit en vedette</label>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn-admin">
                        <?= $editProduct ? 'Modifier le produit' : 'Ajouter le produit' ?>
                    </button>

                    <?php if ($editProduct): ?>
                        <a href="products.php" class="btn-admin" style="background: #6c757d; margin-left: 10px;">Annuler</a>
                    <?php endif; ?>
                </form>
            </div>

            <!-- Products List -->
            <h2>Liste des produits</h2>
            <table class="products-table">
                <thead>
                    <tr>
                        <th>Image</th>
                        <th>Nom</th>
                        <th>Catégorie</th>
                        <th>Prix</th>
                        <th>Stock</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($products as $product): ?>
                        <tr>
                            <td>
                                <img src="../<?= $productObj->getImageUrl($product) ?>" alt="<?= $product['name'] ?>" class="product-image">
                            </td>
                            <td><?= $product['name'] ?></td>
                            <td><?= $product['category_name'] ?? 'N/A' ?></td>
                            <td>
                                <?php if ($product['sale_price']): ?>
                                    <span style="text-decoration: line-through;"><?= number_format($product['price'], 2) ?>€</span>
                                    <strong><?= number_format($product['sale_price'], 2) ?>€</strong>
                                <?php else: ?>
                                    <?= number_format($product['price'], 2) ?>€
                                <?php endif; ?>
                            </td>
                            <td><?= $product['stock'] ?></td>
                            <td>
                                <a href="products.php?edit=<?= $product['id'] ?>" class="btn-admin btn-small">Modifier</a>
                                <form method="POST" style="display: inline;" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')">
                                    <input type="hidden" name="action" value="delete">
                                    <input type="hidden" name="product_id" value="<?= $product['id'] ?>">
                                    <button type="submit" class="btn-admin btn-small btn-danger">Supprimer</button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </main>

    <?php include '../includes/footer.php'; ?>

    <script src="../js/main.js"></script>
</body>
</html>
