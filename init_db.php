<?php
/**
 * Database Initialization Script
 * 
 * This script creates the database and tables for the Nuit Blanche e-commerce website.
 */

// Database connection parameters
$host = 'localhost';
$username = 'root';
$password = '';

try {
    // Connect to MySQL server without selecting a database
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to MySQL server successfully.<br>";
    
    // Read SQL file
    $sql = file_get_contents('database/nuitblanche.sql');
    
    // Execute SQL statements
    $pdo->exec($sql);
    
    echo "Database and tables created successfully.<br>";
    echo "Sample data inserted successfully.<br>";
    
    echo "<p>You can now <a href='index.php'>visit the website</a>.</p>";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "<br>";
}
?>
