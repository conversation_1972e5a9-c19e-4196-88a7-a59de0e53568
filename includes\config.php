<?php
/**
 * Configuration File
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Session configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Set to 1 if using HTTPS

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Site configuration
define('SITE_NAME', 'Nuit Blanche');
define('SITE_URL', 'http://localhost/ProjetPHP');
define('ADMIN_EMAIL', '<EMAIL>');

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'NuitBlanche');
define('DB_USER', 'root');
define('DB_PASS', '');

// Paths
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('CLASSES_PATH', INCLUDES_PATH . '/classes');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('IMAGES_PATH', ROOT_PATH . '/images');

// Image configuration
define('MAX_IMAGE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']);

// Autoloader
spl_autoload_register(function($className) {
    $file = CLASSES_PATH . '/' . $className . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// Initialize Auth
$auth = Auth::getInstance();
