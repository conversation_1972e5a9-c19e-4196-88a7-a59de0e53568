<?php
require_once 'includes/config.php';

// Check if user is logged in
$auth->requireLogin();

// Get current user
$currentUser = $auth->getUser();
$userId = $currentUser['id'];

// Initialize classes
$userClass = new User();
$addressClass = new Address();
$orderClass = new Order();
$wishlistClass = new Wishlist();

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_profile':
                $updateData = [
                    'first_name' => $_POST['first_name'] ?? '',
                    'last_name' => $_POST['last_name'] ?? '',
                    'phone' => $_POST['phone'] ?? '',
                    'birth_date' => $_POST['birth_date'] ?? null
                ];

                if ($userClass->updateProfile($userId, $updateData)) {
                    $message = 'Profil mis à jour avec succès!';
                    $messageType = 'success';
                    // Update session data
                    $currentUser = array_merge($currentUser, $updateData);
                    $auth->setUser($currentUser);
                } else {
                    $message = 'Erreur lors de la mise à jour du profil.';
                    $messageType = 'error';
                }
                break;

            case 'change_password':
                $currentPassword = $_POST['current_password'] ?? '';
                $newPassword = $_POST['new_password'] ?? '';
                $confirmPassword = $_POST['confirm_password'] ?? '';

                if ($newPassword !== $confirmPassword) {
                    $message = 'Les mots de passe ne correspondent pas.';
                    $messageType = 'error';
                } elseif ($userClass->changePassword($userId, $currentPassword, $newPassword)) {
                    $message = 'Mot de passe modifié avec succès!';
                    $messageType = 'success';
                } else {
                    $message = 'Mot de passe actuel incorrect.';
                    $messageType = 'error';
                }
                break;

            case 'add_address':
                $addressData = [
                    'user_id' => $userId,
                    'address_line1' => $_POST['address_line1'] ?? '',
                    'address_line2' => $_POST['address_line2'] ?? '',
                    'city' => $_POST['city'] ?? '',
                    'postal_code' => $_POST['postal_code'] ?? '',
                    'country' => $_POST['country'] ?? '',
                    'phone' => $_POST['phone'] ?? '',
                    'is_default_shipping' => isset($_POST['is_default_shipping']) ? 1 : 0,
                    'is_default_billing' => isset($_POST['is_default_billing']) ? 1 : 0
                ];

                if ($addressClass->addAddress($addressData)) {
                    $message = 'Adresse ajoutée avec succès!';
                    $messageType = 'success';
                } else {
                    $message = 'Erreur lors de l\'ajout de l\'adresse.';
                    $messageType = 'error';
                }
                break;

            case 'update_address':
                $addressId = $_POST['address_id'] ?? 0;
                $addressData = [
                    'address_line1' => $_POST['address_line1'] ?? '',
                    'address_line2' => $_POST['address_line2'] ?? '',
                    'city' => $_POST['city'] ?? '',
                    'postal_code' => $_POST['postal_code'] ?? '',
                    'country' => $_POST['country'] ?? '',
                    'phone' => $_POST['phone'] ?? '',
                    'is_default_shipping' => isset($_POST['is_default_shipping']) ? 1 : 0,
                    'is_default_billing' => isset($_POST['is_default_billing']) ? 1 : 0
                ];

                if ($addressClass->updateAddress($addressId, $userId, $addressData)) {
                    $message = 'Adresse modifiée avec succès!';
                    $messageType = 'success';
                } else {
                    $message = 'Erreur lors de la modification de l\'adresse.';
                    $messageType = 'error';
                }
                break;

            case 'delete_address':
                $addressId = $_POST['address_id'] ?? 0;
                if ($addressClass->deleteAddress($addressId, $userId)) {
                    $message = 'Adresse supprimée avec succès!';
                    $messageType = 'success';
                } else {
                    $message = 'Erreur lors de la suppression de l\'adresse.';
                    $messageType = 'error';
                }
                break;

            case 'remove_wishlist':
                $productId = $_POST['product_id'] ?? 0;
                if ($wishlistClass->removeFromWishlist($userId, $productId)) {
                    $message = 'Produit retiré de la liste de souhaits!';
                    $messageType = 'success';
                } else {
                    $message = 'Erreur lors de la suppression du produit.';
                    $messageType = 'error';
                }
                break;
        }
    }
}

// Get user data
$userAddresses = $addressClass->getUserAddresses($userId);
$userOrders = $orderClass->getUserOrdersWithItems($userId, 10);
$userWishlist = $wishlistClass->getUserWishlist($userId);
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mon Profil - Nuit Blanche</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="profile-page">
    <?php include 'includes/header.php'; ?>

    <main>
        <section class="profile-section">
            <h1>Mon Profil</h1>

            <?php if ($message): ?>
                <div class="message <?php echo $messageType; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <div class="profile-container">
                <!-- Toggle Buttons Navigation -->
                <div class="profile-tabs">
                    <button class="profile-tab-btn active" data-tab="account-info">
                        <span class="tab-icon">👤</span>
                        <span class="tab-text">Informations du compte</span>
                    </button>
                    <button class="profile-tab-btn" data-tab="orders">
                        <span class="tab-icon">📦</span>
                        <span class="tab-text">Mes commandes</span>
                    </button>
                    <button class="profile-tab-btn" data-tab="addresses">
                        <span class="tab-icon">📍</span>
                        <span class="tab-text">Mes adresses</span>
                    </button>
                    <button class="profile-tab-btn" data-tab="wishlist">
                        <span class="tab-icon">❤️</span>
                        <span class="tab-text">Ma liste de souhaits</span>
                    </button>
                    <button class="profile-tab-btn" data-tab="settings">
                        <span class="tab-icon">⚙️</span>
                        <span class="tab-text">Paramètres</span>
                    </button>
                </div>

                <div class="profile-content">
                    <div id="account-info" class="profile-tab active">
                        <div class="section-header">
                            <h2>Informations du compte</h2>
                            <p class="section-description">Gérez vos informations personnelles et votre mot de passe</p>
                        </div>

                        <form class="profile-form" method="POST">
                            <input type="hidden" name="action" value="update_profile">

                            <div class="form-group">
                                <label for="first-name">Prénom</label>
                                <input type="text" id="first-name" name="first_name" value="<?php echo htmlspecialchars($currentUser['first_name'] ?? ''); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="last-name">Nom</label>
                                <input type="text" id="last-name" name="last_name" value="<?php echo htmlspecialchars($currentUser['last_name'] ?? ''); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" value="<?php echo htmlspecialchars($currentUser['email'] ?? ''); ?>" readonly>
                                <small>L'email ne peut pas être modifié</small>
                            </div>

                            <div class="form-group">
                                <label for="phone">Téléphone</label>
                                <input type="tel" id="phone" name="phone" value="<?php echo htmlspecialchars($currentUser['phone'] ?? ''); ?>">
                            </div>

                            <div class="form-group">
                                <label for="birth-date">Date de naissance</label>
                                <input type="date" id="birth-date" name="birth_date" value="<?php echo htmlspecialchars($currentUser['birth_date'] ?? ''); ?>">
                            </div>

                            <button type="submit" class="btn">Sauvegarder les modifications</button>
                        </form>

                        <div class="password-change">
                            <h3>Changer le mot de passe</h3>

                            <form class="password-form" method="POST">
                                <input type="hidden" name="action" value="change_password">

                                <div class="form-group">
                                    <label for="current-password">Mot de passe actuel</label>
                                    <input type="password" id="current-password" name="current_password" required>
                                </div>

                                <div class="form-group">
                                    <label for="new-password">Nouveau mot de passe</label>
                                    <input type="password" id="new-password" name="new_password" required minlength="6">
                                </div>

                                <div class="form-group">
                                    <label for="confirm-password">Confirmer le mot de passe</label>
                                    <input type="password" id="confirm-password" name="confirm_password" required minlength="6">
                                </div>

                                <button type="submit" class="btn-secondary">Changer le mot de passe</button>
                            </form>
                        </div>
                    </div>

                    <div id="orders" class="profile-tab">
                        <div class="section-header">
                            <h2>Mes commandes</h2>
                            <p class="section-description">Consultez l'historique de vos commandes et suivez leur statut</p>
                        </div>

                        <div class="orders-list">
                            <?php if (!empty($userOrders)): ?>
                                <?php foreach ($userOrders as $order): ?>
                                    <?php $statusInfo = $orderClass->formatStatus($order['status']); ?>
                                    <div class="order-item">
                                        <div class="order-header">
                                            <div class="order-number">Commande #<?php echo htmlspecialchars($order['order_number']); ?></div>
                                            <div class="order-date"><?php echo $orderClass->formatDate($order['created_at']); ?></div>
                                            <div class="order-status <?php echo $statusInfo['class']; ?>"><?php echo $statusInfo['label']; ?></div>
                                        </div>

                                        <div class="order-details">
                                            <div class="order-products">
                                                <?php if (!empty($order['items'])): ?>
                                                    <?php foreach ($order['items'] as $item): ?>
                                                        <div class="order-product">
                                                            <img src="<?php echo htmlspecialchars($item['image'] ?: 'images/placeholder.jpg'); ?>"
                                                                 alt="<?php echo htmlspecialchars($item['name']); ?>">
                                                            <div class="product-info">
                                                                <h4><?php echo htmlspecialchars($item['name']); ?></h4>
                                                                <p>Quantité: <?php echo (int)$item['quantity']; ?></p>
                                                                <p>Prix: <?php echo $orderClass->formatPrice($item['price']); ?></p>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </div>

                                            <div class="order-summary">
                                                <p>Total: <?php echo $orderClass->formatPrice($order['total_amount']); ?>
                                                   <?php if ($order['shipping_cost'] > 0): ?>
                                                       (incl. livraison <?php echo $orderClass->formatPrice($order['shipping_cost']); ?>)
                                                   <?php endif; ?>
                                                </p>
                                                <a href="order-details.php?id=<?php echo $order['id']; ?>" class="btn-secondary">Voir les détails</a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>

                        <!-- Empty state (hidden by default, shown when no orders) -->
                        <div class="empty-state" style="display: none;">
                            <div class="empty-icon">📦</div>
                            <h3>Aucune commande trouvée</h3>
                            <p>Vous n'avez pas encore passé de commande. Découvrez notre collection et commencez vos achats !</p>
                            <a href="shop.php" class="btn">Découvrir nos produits</a>
                        </div>
                    </div>

                    <div id="addresses" class="profile-tab">
                        <div class="section-header">
                            <h2>Mes adresses</h2>
                            <p class="section-description">Gérez vos adresses de livraison et de facturation</p>
                        </div>

                        <div class="addresses-container">
                            <?php if (!empty($userAddresses)): ?>
                                <?php foreach ($userAddresses as $address): ?>
                                    <div class="address-card">
                                        <div class="address-type">
                                            <?php if ($address['is_default_shipping']): ?>
                                                <span class="address-badge primary">Livraison par défaut</span>
                                            <?php endif; ?>
                                            <?php if ($address['is_default_billing']): ?>
                                                <span class="address-badge primary">Facturation par défaut</span>
                                            <?php endif; ?>
                                            <h3>Adresse #<?php echo $address['id']; ?></h3>
                                        </div>
                                        <div class="address-details">
                                            <p><strong><?php echo htmlspecialchars($currentUser['first_name'] . ' ' . $currentUser['last_name']); ?></strong></p>
                                            <p><?php echo htmlspecialchars($address['address_line1']); ?></p>
                                            <?php if (!empty($address['address_line2'])): ?>
                                                <p><?php echo htmlspecialchars($address['address_line2']); ?></p>
                                            <?php endif; ?>
                                            <p><?php echo htmlspecialchars($address['postal_code'] . ' ' . $address['city']); ?></p>
                                            <p><?php echo htmlspecialchars($address['country']); ?></p>
                                            <?php if (!empty($address['phone'])): ?>
                                                <p>📞 <?php echo htmlspecialchars($address['phone']); ?></p>
                                            <?php endif; ?>
                                        </div>

                                        <div class="address-actions">
                                            <button class="btn-secondary" onclick="editAddress(<?php echo $address['id']; ?>)">Modifier</button>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette adresse ?');">
                                                <input type="hidden" name="action" value="delete_address">
                                                <input type="hidden" name="address_id" value="<?php echo $address['id']; ?>">
                                                <button type="submit" class="btn-secondary btn-danger">Supprimer</button>
                                            </form>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>

                            <div class="add-address-card">
                                <div class="add-address-content">
                                    <div class="add-icon">➕</div>
                                    <h3>Ajouter une nouvelle adresse</h3>
                                    <p>Ajoutez une adresse de livraison ou de facturation</p>
                                    <button class="btn" onclick="addNewAddress()">Ajouter une adresse</button>
                                </div>
                            </div>
                        </div>

                        <!-- Empty state (hidden by default, shown when no addresses) -->
                        <div class="empty-state" style="display: none;">
                            <div class="empty-icon">📍</div>
                            <h3>Aucune adresse enregistrée</h3>
                            <p>Ajoutez votre première adresse pour faciliter vos commandes futures.</p>
                            <button class="btn">Ajouter une adresse</button>
                        </div>
                    </div>

                    <div id="wishlist" class="profile-tab">
                        <div class="section-header">
                            <h2>Ma liste de souhaits</h2>
                            <p class="section-description">Retrouvez tous vos produits favoris sauvegardés</p>
                        </div>

                        <div class="wishlist-items">
                            <?php if (!empty($userWishlist)): ?>
                                <?php foreach ($userWishlist as $item): ?>
                                    <?php
                                    $priceInfo = $wishlistClass->formatPrice($item['price'], $item['sale_price']);
                                    $availability = $wishlistClass->checkAvailability($item['stock']);
                                    ?>
                                    <div class="product-card wishlist-product">
                                        <div class="product-image">
                                            <img src="<?php echo htmlspecialchars($item['image'] ?: 'images/placeholder.jpg'); ?>"
                                                 alt="<?php echo htmlspecialchars($item['name']); ?>">
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="remove_wishlist">
                                                <input type="hidden" name="product_id" value="<?php echo $item['product_id']; ?>">
                                                <button type="submit" class="remove-wishlist" title="Retirer de la liste"
                                                        onclick="return confirm('Retirer ce produit de votre liste de souhaits ?')">❤️</button>
                                            </form>
                                        </div>
                                        <div class="product-info">
                                            <h3><?php echo htmlspecialchars($item['name']); ?></h3>
                                            <p><?php echo htmlspecialchars($item['description'] ?: 'Aucune description disponible'); ?></p>
                                            <div class="price">
                                                <?php if ($priceInfo['has_sale']): ?>
                                                    <span class="sale-price"><?php echo $priceInfo['current']; ?></span>
                                                    <span class="original-price"><?php echo $priceInfo['original']; ?></span>
                                                    <span class="discount">-<?php echo $priceInfo['discount_percent']; ?>%</span>
                                                <?php else: ?>
                                                    <?php echo $priceInfo['current']; ?>
                                                <?php endif; ?>
                                            </div>
                                            <div class="product-status <?php echo $availability['status']; ?>">
                                                <?php echo $availability['label']; ?>
                                            </div>
                                        </div>
                                        <div class="wishlist-actions">
                                            <?php if ($availability['status'] !== 'out-of-stock'): ?>
                                                <button class="btn" onclick="addToCart(<?php echo $item['product_id']; ?>)">Ajouter au panier</button>
                                            <?php else: ?>
                                                <button class="btn" disabled>Rupture de stock</button>
                                            <?php endif; ?>
                                            <a href="product.php?slug=<?php echo htmlspecialchars($item['slug']); ?>" class="btn-secondary">Voir le produit</a>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>

                        <!-- Empty state (hidden by default, shown when no wishlist items) -->
                        <div class="empty-state" style="display: none;">
                            <div class="empty-icon">❤️</div>
                            <h3>Votre liste de souhaits est vide</h3>
                            <p>Découvrez nos produits et ajoutez vos favoris à votre liste de souhaits.</p>
                            <a href="shop.php" class="btn">Découvrir nos produits</a>
                        </div>
                    </div>

                    <div id="settings" class="profile-tab">
                        <div class="section-header">
                            <h2>Paramètres</h2>
                            <p class="section-description">Personnalisez votre expérience et vos préférences</p>
                        </div>

                        <div class="settings-sections">
                            <div class="settings-section">
                                <h3>Notifications</h3>
                                <div class="settings-form">
                                    <div class="form-group toggle-group">
                                        <div class="toggle-info">
                                            <label>Notifications par email</label>
                                            <p>Recevez des notifications pour vos commandes et promotions</p>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="email-notifications" checked>
                                            <label for="email-notifications"></label>
                                        </div>
                                    </div>

                                    <div class="form-group toggle-group">
                                        <div class="toggle-info">
                                            <label>Newsletter</label>
                                            <p>Restez informé de nos nouveautés et offres spéciales</p>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="newsletter" checked>
                                            <label for="newsletter"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Préférences</h3>
                                <div class="settings-form">
                                    <div class="form-group">
                                        <label for="language">Langue</label>
                                        <select id="language">
                                            <option value="fr" selected>Français</option>
                                            <option value="en">English</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="currency">Devise</label>
                                        <select id="currency">
                                            <option value="eur" selected>EUR (€)</option>
                                            <option value="usd">USD ($)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Confidentialité</h3>
                                <div class="settings-form">
                                    <div class="form-group toggle-group">
                                        <div class="toggle-info">
                                            <label>Profil public</label>
                                            <p>Permettre aux autres utilisateurs de voir votre profil</p>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="public-profile">
                                            <label for="public-profile"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-actions">
                                <button class="btn">Sauvegarder les paramètres</button>
                                <button class="btn-secondary">Réinitialiser</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Address Modal -->
    <div id="address-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Ajouter une adresse</h3>
                <span class="close" onclick="closeAddressModal()">&times;</span>
            </div>
            <form id="address-form" method="POST">
                <input type="hidden" name="action" id="form-action" value="add_address">
                <input type="hidden" name="address_id" id="address-id" value="">

                <div class="form-group">
                    <label for="modal-address-line1">Adresse ligne 1 *</label>
                    <input type="text" id="modal-address-line1" name="address_line1" required>
                </div>

                <div class="form-group">
                    <label for="modal-address-line2">Adresse ligne 2</label>
                    <input type="text" id="modal-address-line2" name="address_line2">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="modal-city">Ville *</label>
                        <input type="text" id="modal-city" name="city" required>
                    </div>
                    <div class="form-group">
                        <label for="modal-postal-code">Code postal *</label>
                        <input type="text" id="modal-postal-code" name="postal_code" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="modal-country">Pays *</label>
                    <select id="modal-country" name="country" required>
                        <option value="">Sélectionner un pays</option>
                        <option value="France">France</option>
                        <option value="Belgique">Belgique</option>
                        <option value="Suisse">Suisse</option>
                        <option value="Luxembourg">Luxembourg</option>
                        <option value="Canada">Canada</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="modal-phone">Téléphone</label>
                    <input type="tel" id="modal-phone" name="phone">
                </div>

                <div class="form-group checkbox-group">
                    <label>
                        <input type="checkbox" id="modal-default-shipping" name="is_default_shipping">
                        Définir comme adresse de livraison par défaut
                    </label>
                </div>

                <div class="form-group checkbox-group">
                    <label>
                        <input type="checkbox" id="modal-default-billing" name="is_default_billing">
                        Définir comme adresse de facturation par défaut
                    </label>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn-secondary" onclick="closeAddressModal()">Annuler</button>
                    <button type="submit" class="btn" id="submit-address">Ajouter l'adresse</button>
                </div>
            </form>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>

    <script src="js/main.js"></script>
    <script src="js/profile.js"></script>
    <script>
        // Initialize empty states based on server data
        document.addEventListener('DOMContentLoaded', function() {
            // Check orders empty state
            <?php if (empty($userOrders)): ?>
                const ordersList = document.querySelector('.orders-list');
                const ordersEmptyState = document.querySelector('#orders .empty-state');
                if (ordersList && ordersEmptyState) {
                    ordersList.style.display = 'none';
                    ordersEmptyState.style.display = 'block';
                }
            <?php endif; ?>

            // Check addresses empty state
            <?php if (empty($userAddresses)): ?>
                const addressesContainer = document.querySelector('.addresses-container');
                const addressesEmptyState = document.querySelector('#addresses .empty-state');
                if (addressesContainer && addressesEmptyState) {
                    addressesContainer.style.display = 'none';
                    addressesEmptyState.style.display = 'block';
                }
            <?php endif; ?>

            // Check wishlist empty state
            <?php if (empty($userWishlist)): ?>
                const wishlistItems = document.querySelector('.wishlist-items');
                const wishlistEmptyState = document.querySelector('#wishlist .empty-state');
                if (wishlistItems && wishlistEmptyState) {
                    wishlistItems.style.display = 'none';
                    wishlistEmptyState.style.display = 'block';
                }
            <?php endif; ?>
        });

        // Functions for address management
        function editAddress(addressId) {
            // Get address data via AJAX
            fetch('ajax/get_address.php?id=' + addressId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        openAddressModal('edit', data.address);
                    } else {
                        alert('Erreur lors du chargement de l\'adresse');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Erreur lors du chargement de l\'adresse');
                });
        }

        function addNewAddress() {
            openAddressModal('add');
        }

        function openAddressModal(mode, addressData = null) {
            const modal = document.getElementById('address-modal');
            const modalTitle = document.getElementById('modal-title');
            const formAction = document.getElementById('form-action');
            const addressId = document.getElementById('address-id');
            const submitButton = document.getElementById('submit-address');

            // Reset form
            document.getElementById('address-form').reset();

            if (mode === 'edit' && addressData) {
                modalTitle.textContent = 'Modifier l\'adresse';
                formAction.value = 'update_address';
                addressId.value = addressData.id;
                submitButton.textContent = 'Modifier l\'adresse';

                // Fill form with existing data
                document.getElementById('modal-address-line1').value = addressData.address_line1 || '';
                document.getElementById('modal-address-line2').value = addressData.address_line2 || '';
                document.getElementById('modal-city').value = addressData.city || '';
                document.getElementById('modal-postal-code').value = addressData.postal_code || '';
                document.getElementById('modal-country').value = addressData.country || '';
                document.getElementById('modal-phone').value = addressData.phone || '';
                document.getElementById('modal-default-shipping').checked = addressData.is_default_shipping == 1;
                document.getElementById('modal-default-billing').checked = addressData.is_default_billing == 1;
            } else {
                modalTitle.textContent = 'Ajouter une adresse';
                formAction.value = 'add_address';
                addressId.value = '';
                submitButton.textContent = 'Ajouter l\'adresse';
            }

            modal.style.display = 'block';
        }

        function closeAddressModal() {
            document.getElementById('address-modal').style.display = 'none';
        }

        // Function for cart management
        function addToCart(productId) {
            // AJAX request to add item to cart
            fetch('ajax/add_to_cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'product_id=' + productId + '&quantity=1'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    alert('Produit ajouté au panier avec succès!');

                    // Update cart count in header if function exists
                    if (typeof updateCartCount === 'function') {
                        updateCartCount(data.cart_count);
                    }
                } else {
                    alert(data.message || 'Erreur lors de l\'ajout au panier');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Erreur lors de l\'ajout au panier');
            });
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('address-modal');
            if (event.target === modal) {
                closeAddressModal();
            }
        }

        // Auto-hide messages after 5 seconds
        <?php if ($message): ?>
            setTimeout(function() {
                const messageElement = document.querySelector('.message');
                if (messageElement) {
                    messageElement.style.opacity = '0';
                    setTimeout(function() {
                        messageElement.style.display = 'none';
                    }, 300);
                }
            }, 5000);
        <?php endif; ?>
    </script>
</body>
</html>
