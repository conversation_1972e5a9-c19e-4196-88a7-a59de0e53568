/* Admin Panel Styles */
.admin-container, .livreur-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

.admin-header, .livreur-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.admin-header h1, .livreur-header h1 {
    margin: 0;
    font-size: 28px;
}

/* Buttons */
.btn-admin, .btn-livreur {
    display: inline-block;
    padding: 12px 24px;
    background: var(--primary-color);
    color: var(--secondary-color);
    text-decoration: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: background-color 0.3s ease;
}

.btn-admin:hover, .btn-livreur:hover {
    background: #333;
}

.btn-small {
    padding: 8px 16px;
    font-size: 12px;
    margin-right: 5px;
}

.btn-danger {
    background: #dc3545;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-success {
    background: #28a745;
}

.btn-success:hover {
    background: #218838;
}

.btn-secondary-admin {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-secondary-admin:hover {
    background: var(--primary-color);
    color: var(--secondary-color);
}

/* Messages */
.message {
    padding: 15px;
    margin-bottom: 20px;
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
}

.message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Statistics */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: #f8f8f8;
    padding: 30px;
    text-align: center;
    border: 1px solid #e0e0e0;
}

.stat-number {
    font-size: 36px;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.stat-label {
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Tables */
.admin-table, .products-table, .categories-table, .orders-table, .users-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 40px;
}

.admin-table th,
.admin-table td,
.products-table th,
.products-table td,
.categories-table th,
.categories-table td,
.orders-table th,
.orders-table td,
.users-table th,
.users-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.admin-table th,
.products-table th,
.categories-table th,
.orders-table th,
.users-table th {
    background: #f8f8f8;
    font-weight: bold;
}

.product-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
}

/* Forms */
.form-container {
    background: #f8f8f8;
    padding: 30px;
    margin-bottom: 40px;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    font-family: inherit;
}

.form-group textarea {
    height: 100px;
    resize: vertical;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.full-width {
    grid-column: 1 / -1;
}

/* Status badges */
.order-status, .role-badge {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: bold;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-processing {
    background: #cce5ff;
    color: #004085;
}

.status-shipped {
    background: #d1ecf1;
    color: #0c5460;
}

.status-delivered {
    background: #d4edda;
    color: #155724;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.role-admin {
    background: #dc3545;
    color: white;
}

.role-livreur {
    background: #007bff;
    color: white;
}

.role-user {
    background: #28a745;
    color: white;
}

/* Action cards */
.admin-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.action-card {
    background: #fff;
    border: 1px solid #e0e0e0;
    padding: 30px;
    text-align: center;
}

.action-card h3 {
    margin-bottom: 15px;
    font-size: 20px;
}

.action-card p {
    margin-bottom: 20px;
    color: #666;
}

/* Responsive */
@media (max-width: 768px) {
    .admin-header, .livreur-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .admin-actions {
        grid-template-columns: 1fr;
    }

    .admin-table, .products-table, .categories-table, .orders-table, .users-table {
        font-size: 12px;
    }

    .admin-table th,
    .admin-table td,
    .products-table th,
    .products-table td,
    .categories-table th,
    .categories-table td,
    .orders-table th,
    .orders-table td,
    .users-table th,
    .users-table td {
        padding: 8px 4px;
    }
}
