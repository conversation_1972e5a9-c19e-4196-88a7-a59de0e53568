<?php
require_once '../includes/config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    echo json_encode([
        'success' => false,
        'message' => 'Vous devez être connecté pour accéder à cette fonctionnalité'
    ]);
    exit;
}

// Check if request method is GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'success' => false,
        'message' => 'Méthode non autorisée'
    ]);
    exit;
}

// Get address ID
$addressId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Validate address ID
if ($addressId <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'ID d\'adresse invalide'
    ]);
    exit;
}

// Get current user ID
$userId = $auth->getUserId();

// Initialize address class
$addressClass = new Address();

// Get address data
$address = $addressClass->getAddress($addressId, $userId);

if ($address) {
    echo json_encode([
        'success' => true,
        'address' => $address
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Adresse non trouvée ou accès non autorisé'
    ]);
}
?>
