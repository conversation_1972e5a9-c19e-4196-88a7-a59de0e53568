/**
 * Nuit Blanche - Product Page JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add to cart functionality
    const addToCartBtn = document.querySelector('.btn-primary');
    if (addToCartBtn) {
        addToCartBtn.addEventListener('click', function() {
            // Get product details
            const productName = document.querySelector('.product-details h1').textContent;
            const productPrice = document.querySelector('.product-price').textContent;
            
            console.log(`Adding to cart: ${productName} - ${productPrice}`);
            
            // Show confirmation
            const confirmation = document.createElement('div');
            confirmation.className = 'cart-confirmation';
            confirmation.innerHTML = `<p>${productName} a été ajouté au panier</p>`;
            
            document.body.appendChild(confirmation);
            
            // Remove confirmation after 3 seconds
            setTimeout(() => {
                confirmation.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(confirmation);
                }, 500);
            }, 3000);
        });
    }
    
    // Product image gallery
    const thumbnails = document.querySelectorAll('.product-thumbnails img');
    const mainImage = document.querySelector('.main-image img');
    
    if (thumbnails.length > 0 && mainImage) {
        thumbnails.forEach(thumbnail => {
            thumbnail.addEventListener('click', function() {
                mainImage.src = this.src.replace('-thumb', '');
                
                // Update active thumbnail
                thumbnails.forEach(thumb => {
                    thumb.classList.remove('active');
                });
                this.classList.add('active');
            });
        });
    }
    
    // Related products carousel (placeholder)
    const prevBtn = document.querySelector('.carousel-prev');
    const nextBtn = document.querySelector('.carousel-next');
    const carousel = document.querySelector('.products-grid');
    
    if (prevBtn && nextBtn && carousel) {
        nextBtn.addEventListener('click', function() {
            carousel.scrollBy({
                left: 300,
                behavior: 'smooth'
            });
        });
        
        prevBtn.addEventListener('click', function() {
            carousel.scrollBy({
                left: -300,
                behavior: 'smooth'
            });
        });
    }
});
