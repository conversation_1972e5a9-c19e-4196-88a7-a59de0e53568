<?php
require_once 'includes/config.php';

// Require user to be logged in
$auth->requireLogin();

// Get order ID from URL
$orderId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$orderId) {
    header('Location: profile.php');
    exit;
}

// Initialize classes
$orderClass = new Order();
$productObj = new Product();

// Get order details
$order = $orderClass->getById($orderId);

// Check if order exists and belongs to current user
if (!$order || $order['user_id'] != $auth->getUser()['id']) {
    header('Location: profile.php');
    exit;
}

// Get order items
$orderItems = $orderClass->getOrderItems($orderId);

$pageTitle = 'Détails de la commande #' . $order['order_number'];
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - <?= SITE_NAME ?></title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .order-details-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .order-header {
            background: #f8f8f8;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
        }

        .order-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .order-status {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .status-pending { background: #fff3cd; color: #856404; }
        .status-processing { background: #cce5ff; color: #004085; }
        .status-shipped { background: #d1ecf1; color: #0c5460; }
        .status-delivered { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }

        .order-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .info-section {
            background: #f8f8f8;
            padding: 20px;
            border: 1px solid #e0e0e0;
        }

        .info-section h3 {
            margin-bottom: 15px;
            font-size: 16px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .info-section p {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .order-items {
            background: #fff;
            border: 1px solid #e0e0e0;
        }

        .order-items h3 {
            background: #f8f8f8;
            padding: 20px;
            margin: 0;
            border-bottom: 1px solid #e0e0e0;
            font-size: 16px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .order-item {
            display: flex;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .order-item:last-child {
            border-bottom: none;
        }

        .item-image {
            width: 80px;
            height: 80px;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .item-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border: 1px solid #e0e0e0;
        }

        .item-details {
            flex-grow: 1;
        }

        .item-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .item-quantity {
            color: #666;
            margin-bottom: 5px;
        }

        .item-price {
            font-weight: bold;
        }

        .order-summary {
            background: #f8f8f8;
            padding: 20px;
            border: 1px solid #e0e0e0;
            margin-top: 30px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .summary-row.total {
            font-weight: bold;
            font-size: 18px;
            border-top: 1px solid #ddd;
            padding-top: 10px;
            margin-top: 10px;
        }

        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: var(--primary-color);
            text-decoration: none;
        }

        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <main>
        <div class="order-details-container">
            <a href="profile.php" class="back-link">← Retour au profil</a>

            <div class="order-header">
                <div class="order-number">Commande #<?= $order['order_number'] ?></div>
                <div class="order-status status-<?= $order['status'] ?>">
                    <?= ucfirst($order['status']) ?>
                </div>
                <p>Passée le <?= date('d/m/Y à H:i', strtotime($order['created_at'])) ?></p>
                <?php if ($order['delivered_at']): ?>
                    <p>Livrée le <?= date('d/m/Y à H:i', strtotime($order['delivered_at'])) ?></p>
                <?php endif; ?>
            </div>

            <div class="order-info-grid">
                <div class="info-section">
                    <h3>Adresse de livraison</h3>
                    <p><strong><?= $order['first_name'] ?> <?= $order['last_name'] ?></strong></p>
                    <p><?= $order['address'] ?></p>
                    <p><?= $order['postal_code'] ?> <?= $order['city'] ?></p>
                    <p><?= $order['phone'] ?></p>
                </div>

                <div class="info-section">
                    <h3>Informations de contact</h3>
                    <p><strong>Email:</strong> <?= $order['email'] ?></p>
                    <p><strong>Téléphone:</strong> <?= $order['phone'] ?></p>
                </div>

                <?php if ($order['livreur_first_name']): ?>
                <div class="info-section">
                    <h3>Livreur assigné</h3>
                    <p><strong><?= $order['livreur_first_name'] ?> <?= $order['livreur_last_name'] ?></strong></p>
                </div>
                <?php endif; ?>
            </div>

            <div class="order-items">
                <h3>Articles commandés</h3>
                <?php foreach ($orderItems as $item): ?>
                    <div class="order-item">
                        <div class="item-image">
                            <img src="<?= $productObj->getImageUrl($item) ?>" alt="<?= htmlspecialchars($item['name']) ?>">
                        </div>
                        <div class="item-details">
                            <div class="item-name"><?= htmlspecialchars($item['name']) ?></div>
                            <div class="item-quantity">Quantité: <?= (int)$item['quantity'] ?></div>
                            <div class="item-price"><?= $orderClass->formatPrice($item['price']) ?> chacun</div>
                        </div>
                        <div class="item-total">
                            <strong><?= $orderClass->formatPrice($item['price'] * $item['quantity']) ?></strong>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <div class="order-summary">
                <div class="summary-row">
                    <span>Sous-total:</span>
                    <span><?= $orderClass->formatPrice($order['total_amount'] - 15) ?></span>
                </div>
                <div class="summary-row">
                    <span>Livraison:</span>
                    <span><?= $orderClass->formatPrice(15) ?></span>
                </div>
                <div class="summary-row total">
                    <span>Total:</span>
                    <span><?= $orderClass->formatPrice($order['total_amount']) ?></span>
                </div>
            </div>
        </div>
    </main>

    <?php include 'includes/footer.php'; ?>
    <script src="js/main.js"></script>
</body>
</html>
