<?php
require_once '../includes/config.php';

// Require admin access
$auth->requireAdmin();

// Get statistics
$productObj = new Product();
$orderObj = new Order();

$totalProducts = count($productObj->getAll());
$totalCategories = count($productObj->getAllCategories());
$pendingOrders = count($orderObj->getPending());
$allOrders = count($orderObj->getAll());

$pageTitle = 'Tableau de bord Admin';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - <?= SITE_NAME ?></title>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .admin-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: #f8f8f8;
            padding: 30px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }
        
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .admin-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .action-card {
            background: #fff;
            border: 1px solid #e0e0e0;
            padding: 30px;
            text-align: center;
        }
        
        .action-card h3 {
            margin-bottom: 15px;
            font-size: 20px;
        }
        
        .action-card p {
            margin-bottom: 20px;
            color: #666;
        }
        
        .btn-admin {
            display: inline-block;
            padding: 12px 24px;
            background: var(--primary-color);
            color: var(--secondary-color);
            text-decoration: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: background-color 0.3s ease;
        }
        
        .btn-admin:hover {
            background: #333;
        }
        
        .btn-secondary-admin {
            background: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }
        
        .btn-secondary-admin:hover {
            background: var(--primary-color);
            color: var(--secondary-color);
        }
    </style>
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>

    <main>
        <div class="admin-container">
            <div class="admin-header">
                <h1>Tableau de bord Admin</h1>
                <p>Bienvenue, <?= $auth->getUser()['first_name'] ?></p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?= $totalProducts ?></div>
                    <div class="stat-label">Produits</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $totalCategories ?></div>
                    <div class="stat-label">Catégories</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $pendingOrders ?></div>
                    <div class="stat-label">Commandes en attente</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $allOrders ?></div>
                    <div class="stat-label">Total commandes</div>
                </div>
            </div>

            <div class="admin-actions">
                <div class="action-card">
                    <h3>Gestion des Produits</h3>
                    <p>Ajouter, modifier ou supprimer des produits de votre catalogue.</p>
                    <a href="products.php" class="btn-admin">Gérer les produits</a>
                </div>

                <div class="action-card">
                    <h3>Gestion des Catégories</h3>
                    <p>Organiser vos produits en créant et gérant les catégories.</p>
                    <a href="categories.php" class="btn-admin">Gérer les catégories</a>
                </div>

                <div class="action-card">
                    <h3>Commandes</h3>
                    <p>Voir et gérer toutes les commandes de votre boutique.</p>
                    <a href="orders.php" class="btn-admin">Voir les commandes</a>
                </div>

                <div class="action-card">
                    <h3>Utilisateurs</h3>
                    <p>Gérer les comptes utilisateurs et les rôles.</p>
                    <a href="users.php" class="btn-secondary-admin">Gérer les utilisateurs</a>
                </div>
            </div>
        </div>
    </main>

    <?php include '../includes/footer.php'; ?>
    
    <script src="../js/main.js"></script>
</body>
</html>
