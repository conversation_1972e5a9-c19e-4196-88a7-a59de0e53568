<?php
/**
 * Product Class
 *
 * Handles product-related operations
 */
class Product {
    private $db;
    private $table = 'products';

    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Get all products
     *
     * @param array $options Optional filtering and sorting options
     * @return array
     */
    public function getAll($options = []) {
        $query = "SELECT p.*, c.name as category_name
                 FROM {$this->table} p
                 LEFT JOIN categories c ON p.category_id = c.id";

        $params = [];
        $whereConditions = [];

        // Filter by category
        if (isset($options['category_id']) && $options['category_id']) {
            $whereConditions[] = "p.category_id = :category_id";
            $params['category_id'] = $options['category_id'];
        }

        // Filter by category slug
        if (isset($options['category_slug']) && $options['category_slug']) {
            // Check if it's a main category (parent) or subcategory
            $category = $this->getCategoryBySlug($options['category_slug']);
            if ($category) {
                if ($category['parent_id'] === null) {
                    // Main category - get all products from its subcategories
                    $whereConditions[] = "c.parent_id = :parent_category_id OR c.id = :category_id";
                    $params['parent_category_id'] = $category['id'];
                    $params['category_id'] = $category['id'];
                } else {
                    // Subcategory - get products from this specific category
                    $whereConditions[] = "c.slug = :category_slug";
                    $params['category_slug'] = $options['category_slug'];
                }
            }
        }

        // Filter by featured
        if (isset($options['featured']) && $options['featured']) {
            $whereConditions[] = "p.is_featured = :featured";
            $params['featured'] = $options['featured'];
        }

        // Filter by sale items
        if (isset($options['on_sale']) && $options['on_sale']) {
            $whereConditions[] = "p.sale_price IS NOT NULL";
        }

        // Filter by search term
        if (isset($options['search']) && $options['search']) {
            $whereConditions[] = "(p.name LIKE :search OR p.description LIKE :search)";
            $params['search'] = '%' . $options['search'] . '%';
        }

        // Add WHERE clause if conditions exist
        if (!empty($whereConditions)) {
            $query .= " WHERE " . implode(' AND ', $whereConditions);
        }

        // Add ORDER BY clause
        if (isset($options['sort_by'])) {
            $sortDirection = isset($options['sort_direction']) ? $options['sort_direction'] : 'ASC';

            switch ($options['sort_by']) {
                case 'price_low':
                    $query .= " ORDER BY p.price ASC";
                    break;
                case 'price_high':
                    $query .= " ORDER BY p.price DESC";
                    break;
                case 'newest':
                    $query .= " ORDER BY p.created_at DESC";
                    break;
                case 'name':
                    $query .= " ORDER BY p.name {$sortDirection}";
                    break;
                case 'discount':
                    $query .= " ORDER BY (p.price - p.sale_price) DESC";
                    break;
                default:
                    $query .= " ORDER BY p.id DESC";
            }
        } else {
            $query .= " ORDER BY p.id DESC";
        }

        // Add LIMIT clause
        if (isset($options['limit']) && $options['limit'] > 0) {
            $query .= " LIMIT :limit";
            $params['limit'] = $options['limit'];

            if (isset($options['offset']) && $options['offset'] >= 0) {
                $query .= " OFFSET :offset";
                $params['offset'] = $options['offset'];
            }
        }

        return $this->db->fetchAll($query, $params);
    }

    /**
     * Get a product by ID
     *
     * @param int $id
     * @return array|false
     */
    public function getById($id) {
        return $this->db->fetchOne(
            "SELECT p.*, c.name as category_name
             FROM {$this->table} p
             LEFT JOIN categories c ON p.category_id = c.id
             WHERE p.id = :id",
            ['id' => $id]
        );
    }

    /**
     * Alias for getById for consistency
     *
     * @param int $id
     * @return array|false
     */
    public function findById($id) {
        return $this->getById($id);
    }

    /**
     * Get a product by slug
     *
     * @param string $slug
     * @return array|false
     */
    public function getBySlug($slug) {
        return $this->db->fetchOne(
            "SELECT p.*, c.name as category_name
             FROM {$this->table} p
             LEFT JOIN categories c ON p.category_id = c.id
             WHERE p.slug = :slug",
            ['slug' => $slug]
        );
    }

    /**
     * Get featured products
     *
     * @param int $limit
     * @return array
     */
    public function getFeatured($limit = 4) {
        return $this->getAll([
            'featured' => 1,
            'limit' => $limit
        ]);
    }

    /**
     * Get sale products
     *
     * @param int $limit
     * @return array
     */
    public function getOnSale($limit = 6) {
        return $this->getAll([
            'on_sale' => true,
            'limit' => $limit
        ]);
    }

    /**
     * Get related products
     *
     * @param int $productId
     * @param int $limit
     * @return array
     */
    public function getRelated($productId, $limit = 4) {
        $product = $this->getById($productId);

        if (!$product) {
            return [];
        }

        return $this->db->fetchAll(
            "SELECT p.*, c.name as category_name
             FROM {$this->table} p
             LEFT JOIN categories c ON p.category_id = c.id
             WHERE p.category_id = :category_id AND p.id != :product_id
             ORDER BY RAND()
             LIMIT :limit",
            [
                'category_id' => $product['category_id'],
                'product_id' => $productId,
                'limit' => $limit
            ]
        );
    }

    /**
     * Get product images
     *
     * @param int $productId
     * @return array
     */
    public function getImages($productId) {
        return $this->db->fetchAll(
            "SELECT * FROM product_images WHERE product_id = :product_id ORDER BY is_primary DESC",
            ['product_id' => $productId]
        );
    }

    /**
     * Get all categories
     *
     * @return array
     */
    public function getAllCategories() {
        return $this->db->fetchAll("SELECT * FROM categories ORDER BY parent_id, name");
    }

    /**
     * Get main categories (parent categories)
     *
     * @return array
     */
    public function getMainCategories() {
        return $this->db->fetchAll("SELECT * FROM categories WHERE parent_id IS NULL ORDER BY name");
    }

    /**
     * Get subcategories for a parent category
     *
     * @param int $parentId
     * @return array
     */
    public function getSubcategories($parentId) {
        return $this->db->fetchAll(
            "SELECT * FROM categories WHERE parent_id = :parent_id ORDER BY name",
            ['parent_id' => $parentId]
        );
    }

    /**
     * Get category hierarchy (main categories with their subcategories)
     *
     * @return array
     */
    public function getCategoryHierarchy() {
        $mainCategories = $this->getMainCategories();
        $hierarchy = [];

        foreach ($mainCategories as $mainCategory) {
            $mainCategory['subcategories'] = $this->getSubcategories($mainCategory['id']);
            $hierarchy[] = $mainCategory;
        }

        return $hierarchy;
    }

    /**
     * Get category by slug
     *
     * @param string $slug
     * @return array|false
     */
    public function getCategoryBySlug($slug) {
        return $this->db->fetchOne(
            "SELECT * FROM categories WHERE slug = :slug",
            ['slug' => $slug]
        );
    }

    /**
     * Create a new category
     *
     * @param array $categoryData
     * @return int|false Category ID or false on failure
     */
    public function createCategory($categoryData) {
        return $this->db->insert('categories', $categoryData);
    }

    /**
     * Update a category
     *
     * @param int $categoryId
     * @param array $categoryData
     * @return bool
     */
    public function updateCategory($categoryId, $categoryData) {
        return $this->db->update(
            'categories',
            $categoryData,
            'id = :id',
            ['id' => $categoryId]
        );
    }

    /**
     * Delete a category
     *
     * @param int $categoryId
     * @return bool
     */
    public function deleteCategory($categoryId) {
        return $this->db->delete(
            'categories',
            'id = :id',
            ['id' => $categoryId]
        );
    }

    /**
     * Create a new product
     *
     * @param array $productData
     * @return int|false Product ID or false on failure
     */
    public function create($productData) {
        return $this->db->insert('products', $productData);
    }

    /**
     * Update a product
     *
     * @param int $productId
     * @param array $productData
     * @return bool
     */
    public function update($productId, $productData) {
        return $this->db->update(
            'products',
            $productData,
            'id = :id',
            ['id' => $productId]
        );
    }

    /**
     * Update product stock
     *
     * @param int $productId
     * @param int $stockChange (positive to increase, negative to decrease)
     * @return bool
     */
    public function updateStock($productId, $stockChange) {
        // Get current stock
        $product = $this->getById($productId);
        if (!$product) {
            return false;
        }

        $newStock = $product['stock'] + $stockChange;

        // Ensure stock doesn't go below 0
        if ($newStock < 0) {
            $newStock = 0;
        }

        return $this->db->update(
            'products',
            ['stock' => $newStock],
            'id = :id',
            ['id' => $productId]
        );
    }

    /**
     * Delete a product
     *
     * @param int $productId
     * @return bool
     */
    public function delete($productId) {
        return $this->db->delete(
            'products',
            'id = :id',
            ['id' => $productId]
        );
    }
}
