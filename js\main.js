/**
 * Nuit Blanche - Main JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const menuToggle = document.querySelector('.menu-toggle');
    const mainNav = document.querySelector('.main-nav');

    if (menuToggle && mainNav) {
        menuToggle.addEventListener('click', function() {
            mainNav.classList.toggle('active');
        });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            if (targetId === '#') return;

            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 100,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Recommendation form modal
    const recommendationBtn = document.querySelector('.recommendation-form button');
    if (recommendationBtn) {
        recommendationBtn.addEventListener('click', function() {
            openRecommendationModal();
        });
    }

    // Function to open recommendation modal
    function openRecommendationModal() {
        // Create modal HTML
        const modalHTML = `
            <div id="recommendation-modal" class="modal" style="display: block;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Envoyer une recommandation</h3>
                        <span class="close" onclick="closeRecommendationModal()">&times;</span>
                    </div>
                    <form id="recommendation-form" onsubmit="submitRecommendation(event)">
                        <div class="form-group">
                            <label for="rec-name">Votre nom *</label>
                            <input type="text" id="rec-name" name="name" required>
                        </div>

                        <div class="form-group">
                            <label for="rec-email">Votre email *</label>
                            <input type="email" id="rec-email" name="email" required>
                        </div>

                        <div class="form-group">
                            <label for="rec-rating">Note (sur 5) *</label>
                            <select id="rec-rating" name="rating" required>
                                <option value="">Choisir une note</option>
                                <option value="5">⭐⭐⭐⭐⭐ (5/5)</option>
                                <option value="4">⭐⭐⭐⭐ (4/5)</option>
                                <option value="3">⭐⭐⭐ (3/5)</option>
                                <option value="2">⭐⭐ (2/5)</option>
                                <option value="1">⭐ (1/5)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="rec-message">Votre recommandation *</label>
                            <textarea id="rec-message" name="message" rows="4" required placeholder="Partagez votre expérience avec Nuit Blanche..."></textarea>
                        </div>

                        <div class="modal-actions">
                            <button type="button" class="btn-secondary" onclick="closeRecommendationModal()">Annuler</button>
                            <button type="submit" class="btn">Envoyer</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    // Function to close recommendation modal
    window.closeRecommendationModal = function() {
        const modal = document.getElementById('recommendation-modal');
        if (modal) {
            modal.remove();
        }
    }

    // Function to submit recommendation
    window.submitRecommendation = function(event) {
        event.preventDefault();

        const form = event.target;
        const formData = new FormData(form);
        const submitButton = form.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;

        // Show loading state
        submitButton.textContent = 'Envoi en cours...';
        submitButton.disabled = true;

        // Simulate form submission (replace with actual AJAX call)
        setTimeout(() => {
            // Show success message
            alert('Merci pour votre recommandation ! Elle sera examinée avant publication.');

            // Close modal
            closeRecommendationModal();
        }, 1500);
    }

    // Close modal when clicking outside
    document.addEventListener('click', function(event) {
        const modal = document.getElementById('recommendation-modal');
        if (modal && event.target === modal) {
            closeRecommendationModal();
        }
    });

    // Image lazy loading
    const lazyImages = document.querySelectorAll('img[data-src]');
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const image = entry.target;
                    image.src = image.dataset.src;
                    image.removeAttribute('data-src');
                    imageObserver.unobserve(image);
                }
            });
        });

        lazyImages.forEach(image => {
            imageObserver.observe(image);
        });
    } else {
        // Fallback for browsers that don't support IntersectionObserver
        lazyImages.forEach(image => {
            image.src = image.dataset.src;
            image.removeAttribute('data-src');
        });
    }
});
