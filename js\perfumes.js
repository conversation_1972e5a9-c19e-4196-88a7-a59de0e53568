/**
 * Nuit Blanche - Perfumes Page JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Size selection
    const sizeOptions = document.querySelectorAll('.size-option input');
    
    sizeOptions.forEach(option => {
        option.addEventListener('change', function() {
            const perfumeCard = this.closest('.perfume-card');
            const perfumeName = perfumeCard.querySelector('h3').textContent;
            const selectedSize = this.id.includes('50') ? '50ml' : '100ml';
            const selectedPrice = this.id.includes('50') ? '$85' : '$120';
            
            console.log(`Selected ${selectedSize} of ${perfumeName} for ${selectedPrice}`);
        });
    });
    
    // Add to cart buttons
    const addToCartButtons = document.querySelectorAll('.perfume-details .btn-secondary');
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function() {
            const perfumeCard = this.closest('.perfume-card');
            const perfumeName = perfumeCard.querySelector('h3').textContent;
            const selectedSize = perfumeCard.querySelector('input:checked').id.includes('50') ? '50ml' : '100ml';
            const selectedPrice = perfumeCard.querySelector('input:checked').id.includes('50') ? '$85' : '$120';
            
            console.log(`Adding to cart: ${perfumeName} (${selectedSize}) - ${selectedPrice}`);
            
            // Show confirmation
            const confirmation = document.createElement('div');
            confirmation.className = 'cart-confirmation';
            confirmation.innerHTML = `<p>${perfumeName} (${selectedSize}) a été ajouté au panier</p>`;
            
            document.body.appendChild(confirmation);
            
            // Remove confirmation after 3 seconds
            setTimeout(() => {
                confirmation.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(confirmation);
                }, 500);
            }, 3000);
        });
    });
    
    // Add set to cart button
    const addSetButton = document.querySelector('.set-details .btn');
    if (addSetButton) {
        addSetButton.addEventListener('click', function() {
            const setName = this.closest('.set-details').querySelector('h3').textContent;
            const setPrice = this.closest('.set-details').querySelector('.set-price').textContent;
            
            console.log(`Adding to cart: ${setName} - ${setPrice}`);
            
            // Show confirmation
            const confirmation = document.createElement('div');
            confirmation.className = 'cart-confirmation';
            confirmation.innerHTML = `<p>${setName} a été ajouté au panier</p>`;
            
            document.body.appendChild(confirmation);
            
            // Remove confirmation after 3 seconds
            setTimeout(() => {
                confirmation.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(confirmation);
                }, 500);
            }, 3000);
        });
    }
    
    // Learn more link
    const learnMoreLink = document.querySelector('.ingredients-text .btn-secondary');
    if (learnMoreLink) {
        learnMoreLink.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Create modal
            const modal = document.createElement('div');
            modal.className = 'ingredients-modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <span class="close-modal">&times;</span>
                    <h2>Nos ingrédients</h2>
                    <p>Chez Nuit Blanche, nous croyons en la transparence et en l'utilisation d'ingrédients de haute qualité pour nos parfums.</p>
                    
                    <h3>Ingrédients naturels</h3>
                    <ul>
                        <li><strong>Fleur d'oranger</strong> - Récoltée à la main dans le sud de la France</li>
                        <li><strong>Jasmin</strong> - Cultivé de manière durable en Inde</li>
                        <li><strong>Bois de santal</strong> - Issu de forêts gérées durablement en Australie</li>
                        <li><strong>Vanille</strong> - Cultivée à Madagascar par des coopératives locales</li>
                        <li><strong>Lavande</strong> - Récoltée dans les champs de Provence</li>
                        <li><strong>Bergamote</strong> - Cultivée en Calabre, Italie</li>
                    </ul>
                    
                    <h3>Notre engagement</h3>
                    <p>Tous nos parfums sont:</p>
                    <ul>
                        <li>Sans phtalates</li>
                        <li>Sans parabènes</li>
                        <li>Sans colorants artificiels</li>
                        <li>Végans</li>
                        <li>Non testés sur les animaux</li>
                    </ul>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Close modal functionality
            const closeModal = modal.querySelector('.close-modal');
            closeModal.addEventListener('click', function() {
                document.body.removeChild(modal);
            });
            
            // Close modal when clicking outside
            window.addEventListener('click', function(event) {
                if (event.target === modal) {
                    document.body.removeChild(modal);
                }
            });
        });
    }
});
