<?php
require_once '../includes/config.php';

// Require admin access
$auth->requireAdmin();

$userObj = new User();
$message = '';
$error = '';

// Handle user role updates
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'update_role') {
        $userId = $_POST['user_id'];
        $newRole = $_POST['role'];

        // Don't allow changing own role
        if ($userId == $auth->getUserId()) {
            $error = 'Vous ne pouvez pas modifier votre propre rôle.';
        } else {
            // Update user role using Database class directly
            $db = Database::getInstance();
            if ($db->update('users', ['role' => $newRole], 'id = :id', ['id' => $userId])) {
                $message = 'Rôle utilisateur mis à jour avec succès.';
            } else {
                $error = 'Erreur lors de la mise à jour du rôle.';
            }
        }
    }
}

// Get all users
$db = Database::getInstance();
$users = $db->fetchAll("SELECT * FROM users ORDER BY created_at DESC");

$pageTitle = 'Gestion des Utilisateurs';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - <?= SITE_NAME ?></title>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .btn-admin {
            display: inline-block;
            padding: 12px 24px;
            background: var(--primary-color);
            color: var(--secondary-color);
            text-decoration: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-admin:hover {
            background: #333;
        }

        .message {
            padding: 15px;
            margin-bottom: 20px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 40px;
        }

        .users-table th,
        .users-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .users-table th {
            background: #f8f8f8;
            font-weight: bold;
        }

        .role-badge {
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: bold;
        }

        .role-admin {
            background: #dc3545;
            color: white;
        }

        .role-livreur {
            background: #007bff;
            color: white;
        }

        .role-customer {
            background: #28a745;
            color: white;
        }

        .role-select {
            padding: 4px;
            border: 1px solid #ddd;
            font-size: 12px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #f8f8f8;
            padding: 20px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
    </style>
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>

    <main>
        <div class="admin-container">
            <div class="admin-header">
                <h1><?= $pageTitle ?></h1>
                <a href="dashboard.php" class="btn-admin">Retour au tableau de bord</a>
            </div>

            <?php if ($message): ?>
                <div class="message"><?= $message ?></div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="message error"><?= $error ?></div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="stats-grid">
                <?php
                $roleCounts = [
                    '"ADMIN"' => 0,
                    '"LIVREUR"' => 0,
                    '"USER"' => 0
                ];

                foreach ($users as $user) {
                    $roleCounts[$user['role']]++;
                }
                ?>
                <div class="stat-card">
                    <div class="stat-number"><?= $roleCounts['"ADMIN"'] ?></div>
                    <div class="stat-label">Administrateurs</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $roleCounts['"LIVREUR"'] ?></div>
                    <div class="stat-label">Livreurs</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $roleCounts['"USER"'] ?></div>
                    <div class="stat-label">Clients</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= count($users) ?></div>
                    <div class="stat-label">Total</div>
                </div>
            </div>

            <!-- Users Table -->
            <table class="users-table">
                <thead>
                    <tr>
                        <th>Nom</th>
                        <th>Email</th>
                        <th>Rôle</th>
                        <th>Date d'inscription</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?= $user['first_name'] ?> <?= $user['last_name'] ?></td>
                            <td><?= $user['email'] ?></td>
                            <td>
                                <span class="role-badge role-<?= str_replace('"', '', strtolower($user['role'])) ?>">
                                    <?= str_replace('"', '', $user['role']) ?>
                                </span>
                            </td>
                            <td><?= date('d/m/Y H:i', strtotime($user['created_at'])) ?></td>
                            <td>
                                <?php if ($user['id'] != $auth->getUserId()): ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="update_role">
                                        <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                                        <select name="role" class="role-select" onchange="this.form.submit()">
                                            <option value='"USER"' <?= $user['role'] === '"USER"' ? 'selected' : '' ?>>Client</option>
                                            <option value='"LIVREUR"' <?= $user['role'] === '"LIVREUR"' ? 'selected' : '' ?>>Livreur</option>
                                            <option value='"ADMIN"' <?= $user['role'] === '"ADMIN"' ? 'selected' : '' ?>>Admin</option>
                                        </select>
                                    </form>
                                <?php else: ?>
                                    <em style="color: #666;">Votre compte</em>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </main>

    <?php include '../includes/footer.php'; ?>

    <script src="../js/main.js"></script>
</body>
</html>
