<?php
/**
 * Wishlist Class
 * 
 * Handles wishlist-related operations
 */
class Wishlist {
    private $db;
    private $table = 'wishlist';
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get all wishlist items for a user
     * 
     * @param int $userId
     * @return array
     */
    public function getUserWishlist($userId) {
        return $this->db->fetchAll(
            "SELECT w.*, p.name, p.slug, p.description, p.price, p.sale_price, p.image, p.stock,
                    c.name as category_name
             FROM {$this->table} w
             JOIN products p ON w.product_id = p.id
             LEFT JOIN categories c ON p.category_id = c.id
             WHERE w.user_id = :user_id
             ORDER BY w.created_at DESC",
            ['user_id' => $userId]
        );
    }
    
    /**
     * Add product to wishlist
     * 
     * @param int $userId
     * @param int $productId
     * @return bool
     */
    public function addToWishlist($userId, $productId) {
        // Check if already in wishlist
        if ($this->isInWishlist($userId, $productId)) {
            return false;
        }
        
        $result = $this->db->insert($this->table, [
            'user_id' => $userId,
            'product_id' => $productId
        ]);
        
        return $result !== false;
    }
    
    /**
     * Remove product from wishlist
     * 
     * @param int $userId
     * @param int $productId
     * @return bool
     */
    public function removeFromWishlist($userId, $productId) {
        return $this->db->delete(
            $this->table,
            'user_id = :user_id AND product_id = :product_id',
            ['user_id' => $userId, 'product_id' => $productId]
        );
    }
    
    /**
     * Check if product is in user's wishlist
     * 
     * @param int $userId
     * @param int $productId
     * @return bool
     */
    public function isInWishlist($userId, $productId) {
        $result = $this->db->fetchOne(
            "SELECT id FROM {$this->table} WHERE user_id = :user_id AND product_id = :product_id",
            ['user_id' => $userId, 'product_id' => $productId]
        );
        
        return $result !== false;
    }
    
    /**
     * Get wishlist count for a user
     * 
     * @param int $userId
     * @return int
     */
    public function getWishlistCount($userId) {
        $result = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM {$this->table} WHERE user_id = :user_id",
            ['user_id' => $userId]
        );
        
        return $result ? (int)$result['count'] : 0;
    }
    
    /**
     * Clear entire wishlist for a user
     * 
     * @param int $userId
     * @return bool
     */
    public function clearWishlist($userId) {
        return $this->db->delete(
            $this->table,
            'user_id = :user_id',
            ['user_id' => $userId]
        );
    }
    
    /**
     * Get wishlist item by ID
     * 
     * @param int $wishlistId
     * @param int $userId
     * @return array|false
     */
    public function getWishlistItem($wishlistId, $userId) {
        return $this->db->fetchOne(
            "SELECT w.*, p.name, p.slug, p.description, p.price, p.sale_price, p.image, p.stock
             FROM {$this->table} w
             JOIN products p ON w.product_id = p.id
             WHERE w.id = :id AND w.user_id = :user_id",
            ['id' => $wishlistId, 'user_id' => $userId]
        );
    }
    
    /**
     * Format price for display
     * 
     * @param float $price
     * @param float $salePrice
     * @return array
     */
    public function formatPrice($price, $salePrice = null) {
        $formatted = [
            'original' => number_format($price, 2) . ' €',
            'current' => number_format($salePrice ?: $price, 2) . ' €',
            'has_sale' => !empty($salePrice) && $salePrice < $price
        ];
        
        if ($formatted['has_sale']) {
            $formatted['discount_percent'] = round((($price - $salePrice) / $price) * 100);
        }
        
        return $formatted;
    }
    
    /**
     * Check product availability
     * 
     * @param int $stock
     * @return array
     */
    public function checkAvailability($stock) {
        if ($stock > 10) {
            return ['status' => 'in-stock', 'label' => 'En stock'];
        } elseif ($stock > 0) {
            return ['status' => 'low-stock', 'label' => 'Stock limité'];
        } else {
            return ['status' => 'out-of-stock', 'label' => 'Rupture de stock'];
        }
    }
    
    /**
     * Move wishlist items to cart (if cart functionality exists)
     * 
     * @param int $userId
     * @param array $productIds
     * @return bool
     */
    public function moveToCart($userId, $productIds = []) {
        // This would integrate with cart functionality when implemented
        // For now, just remove from wishlist
        if (empty($productIds)) {
            // Move all items
            return $this->clearWishlist($userId);
        } else {
            // Move specific items
            $success = true;
            foreach ($productIds as $productId) {
                if (!$this->removeFromWishlist($userId, $productId)) {
                    $success = false;
                }
            }
            return $success;
        }
    }
}
